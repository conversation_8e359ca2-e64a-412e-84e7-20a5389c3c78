#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
微博国际版私信发送工具 - GUI版本
支持发送文字和图片消息的图形界面版本
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import threading
import os
from weibo_sender import WeiboSender


class WeiboSenderGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("微博国际版私信发送工具")
        self.root.geometry("800x700")
        self.root.resizable(True, True)
        
        # 设置样式
        style = ttk.Style()
        style.theme_use('clam')
        
        self.sender = None
        self.selected_image_path = None
        
        self.create_widgets()
        self.load_config()
    
    def create_widgets(self):
        """创建界面组件"""
        # 创建主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        
        # 标题
        title_label = ttk.Label(main_frame, text="微博国际版私信发送工具", 
                               font=('Arial', 16, 'bold'))
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 20))
        
        # 配置参数区域
        config_frame = ttk.LabelFrame(main_frame, text="配置参数", padding="10")
        config_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        config_frame.columnconfigure(1, weight=1)
        
        # UID
        ttk.Label(config_frame, text="你的UID:").grid(row=0, column=0, sticky=tk.W, pady=2)
        self.uid_var = tk.StringVar()
        ttk.Entry(config_frame, textvariable=self.uid_var, width=50).grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(10, 0), pady=2)
        
        # GSID
        ttk.Label(config_frame, text="GSID:").grid(row=1, column=0, sticky=tk.W, pady=2)
        self.gsid_var = tk.StringVar()
        ttk.Entry(config_frame, textvariable=self.gsid_var, width=50).grid(row=1, column=1, sticky=(tk.W, tk.E), padx=(10, 0), pady=2)
        
        # S值
        ttk.Label(config_frame, text="S值:").grid(row=2, column=0, sticky=tk.W, pady=2)
        self.s_value_var = tk.StringVar(value="dddddddd")
        ttk.Entry(config_frame, textvariable=self.s_value_var, width=50).grid(row=2, column=1, sticky=(tk.W, tk.E), padx=(10, 0), pady=2)
        
        # AID
        ttk.Label(config_frame, text="AID:").grid(row=3, column=0, sticky=tk.W, pady=2)
        self.aid_var = tk.StringVar()
        ttk.Entry(config_frame, textvariable=self.aid_var, width=50).grid(row=3, column=1, sticky=(tk.W, tk.E), padx=(10, 0), pady=2)
        
        # SUB Cookie
        ttk.Label(config_frame, text="SUB Cookie:").grid(row=4, column=0, sticky=tk.W, pady=2)
        self.sub_var = tk.StringVar()
        ttk.Entry(config_frame, textvariable=self.sub_var, width=50).grid(row=4, column=1, sticky=(tk.W, tk.E), padx=(10, 0), pady=2)
        
        # SUBP Cookie (可选)
        ttk.Label(config_frame, text="SUBP Cookie (可选):").grid(row=5, column=0, sticky=tk.W, pady=2)
        self.subp_var = tk.StringVar()
        ttk.Entry(config_frame, textvariable=self.subp_var, width=50).grid(row=5, column=1, sticky=(tk.W, tk.E), padx=(10, 0), pady=2)
        
        # 保存配置按钮
        ttk.Button(config_frame, text="保存配置", command=self.save_config).grid(row=6, column=1, sticky=tk.E, pady=(10, 0))
        
        # 发送消息区域
        send_frame = ttk.LabelFrame(main_frame, text="发送消息", padding="10")
        send_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        send_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(2, weight=1)
        
        # 接收者UID
        ttk.Label(send_frame, text="接收者UID:").grid(row=0, column=0, sticky=tk.W, pady=2)
        self.recipient_var = tk.StringVar()
        ttk.Entry(send_frame, textvariable=self.recipient_var, width=30).grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(10, 0), pady=2)
        
        # 文字消息
        ttk.Label(send_frame, text="文字消息:").grid(row=1, column=0, sticky=(tk.W, tk.N), pady=(10, 2))
        self.text_content = scrolledtext.ScrolledText(send_frame, height=6, width=50)
        self.text_content.grid(row=1, column=1, sticky=(tk.W, tk.E), padx=(10, 0), pady=(10, 2))
        
        # 发送文字按钮
        ttk.Button(send_frame, text="📝 发送文字消息", command=self.send_text_message).grid(row=2, column=1, sticky=tk.E, pady=(5, 0))
        
        # 图片选择区域
        ttk.Label(send_frame, text="选择图片:").grid(row=3, column=0, sticky=(tk.W, tk.N), pady=(15, 2))
        image_frame = ttk.Frame(send_frame)
        image_frame.grid(row=3, column=1, sticky=(tk.W, tk.E), padx=(10, 0), pady=(15, 2))
        image_frame.columnconfigure(0, weight=1)

        # 图片路径显示
        self.image_path_var = tk.StringVar(value="未选择图片")
        image_label = ttk.Label(image_frame, textvariable=self.image_path_var, foreground="gray",
                               relief="sunken", padding="5")
        image_label.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 5))

        # 图片选择按钮
        button_frame = ttk.Frame(image_frame)
        button_frame.grid(row=1, column=0, sticky=(tk.W, tk.E))

        ttk.Button(button_frame, text="📁 选择图片", command=self.select_image).grid(row=0, column=0, sticky=tk.W)
        ttk.Button(button_frame, text="🗑️ 清除选择", command=self.clear_image_selection).grid(row=0, column=1, padx=(10, 0))

        # 发送图片按钮
        ttk.Button(send_frame, text="📤 发送图片", command=self.send_image_message).grid(row=4, column=1, sticky=tk.E, pady=(10, 0))
        
        # 日志区域
        log_frame = ttk.LabelFrame(main_frame, text="操作日志", padding="10")
        log_frame.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S))
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
        
        self.log_text = scrolledtext.ScrolledText(log_frame, height=8, width=80)
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 清空日志按钮
        ttk.Button(log_frame, text="清空日志", command=self.clear_log).grid(row=1, column=0, sticky=tk.E, pady=(5, 0))
    
    def log_message(self, message):
        """添加日志消息"""
        self.log_text.insert(tk.END, f"{message}\n")
        self.log_text.see(tk.END)
        self.root.update_idletasks()
    
    def clear_log(self):
        """清空日志"""
        self.log_text.delete(1.0, tk.END)
    
    def save_config(self):
        """保存配置到文件"""
        config_data = {
            'uid': self.uid_var.get(),
            'gsid': self.gsid_var.get(),
            's_value': self.s_value_var.get(),
            'aid': self.aid_var.get(),
            'sub': self.sub_var.get(),
            'subp': self.subp_var.get(),
            'recipient': self.recipient_var.get()
        }
        
        try:
            with open('weibo_config.txt', 'w', encoding='utf-8') as f:
                for key, value in config_data.items():
                    f.write(f"{key}={value}\n")
            self.log_message("✅ 配置已保存到 weibo_config.txt")
        except Exception as e:
            self.log_message(f"❌ 保存配置失败: {e}")
    
    def load_config(self):
        """从文件加载配置"""
        try:
            if os.path.exists('weibo_config.txt'):
                with open('weibo_config.txt', 'r', encoding='utf-8') as f:
                    for line in f:
                        line = line.strip()
                        if '=' in line:
                            key, value = line.split('=', 1)
                            if key == 'uid':
                                self.uid_var.set(value)
                            elif key == 'gsid':
                                self.gsid_var.set(value)
                            elif key == 's_value':
                                self.s_value_var.set(value)
                            elif key == 'aid':
                                self.aid_var.set(value)
                            elif key == 'sub':
                                self.sub_var.set(value)
                            elif key == 'subp':
                                self.subp_var.set(value)
                            elif key == 'recipient':
                                self.recipient_var.set(value)
                self.log_message("✅ 已加载保存的配置")
        except Exception as e:
            self.log_message(f"⚠️ 加载配置失败: {e}")
    
    def create_sender(self):
        """创建发送器实例"""
        if not all([self.uid_var.get(), self.gsid_var.get(), self.s_value_var.get(), 
                   self.aid_var.get(), self.sub_var.get()]):
            messagebox.showerror("错误", "请填写所有必需的配置参数！")
            return False
        
        try:
            self.sender = WeiboSender(
                uid=self.uid_var.get(),
                gsid=self.gsid_var.get(),
                s_value=self.s_value_var.get(),
                aid=self.aid_var.get()
            )
            
            # 设置Cookie
            subp = self.subp_var.get() if self.subp_var.get() else None
            self.sender.set_cookies(self.sub_var.get(), subp)
            
            return True
        except Exception as e:
            messagebox.showerror("错误", f"创建发送器失败: {e}")
            return False
    
    def select_image(self):
        """选择图片文件"""
        try:
            self.log_message("🔍 打开文件选择对话框...")

            file_path = filedialog.askopenfilename(
                title="选择图片文件",
                filetypes=[
                    ("图片文件", "*.jpg *.jpeg *.png *.gif *.bmp *.webp"),
                    ("JPEG文件", "*.jpg *.jpeg"),
                    ("PNG文件", "*.png"),
                    ("所有文件", "*.*")
                ],
                initialdir=os.getcwd()  # 从当前目录开始
            )

            if file_path:
                self.selected_image_path = file_path
                filename = os.path.basename(file_path)
                self.image_path_var.set(f"已选择: {filename}")
                self.log_message(f"📁 已选择图片: {filename}")
                self.log_message(f"📍 文件路径: {file_path}")
            else:
                self.log_message("❌ 未选择任何文件")

        except Exception as e:
            self.log_message(f"❌ 选择图片时出错: {e}")
            messagebox.showerror("错误", f"选择图片时出错: {e}")

    def clear_image_selection(self):
        """清除图片选择"""
        self.selected_image_path = None
        self.image_path_var.set("未选择图片")
        self.log_message("🗑️ 已清除图片选择")
    
    def send_text_message(self):
        """发送文字消息"""
        if not self.recipient_var.get():
            messagebox.showerror("错误", "请输入接收者UID！")
            return
        
        text_content = self.text_content.get(1.0, tk.END).strip()
        if not text_content:
            messagebox.showerror("错误", "请输入要发送的文字内容！")
            return
        
        # 在新线程中执行发送操作
        threading.Thread(target=self._send_text_thread, args=(text_content,), daemon=True).start()
    
    def _send_text_thread(self, text_content):
        """在线程中发送文字消息"""
        try:
            self.log_message("📝 开始发送文字消息...")
            
            if not self.create_sender():
                return
            
            result = self.sender.send_text(self.recipient_var.get(), text_content)
            
            if 'error' in result:
                self.log_message(f"❌ 文字消息发送失败: {result['error']}")
                messagebox.showerror("发送失败", f"文字消息发送失败: {result['error']}")
            else:
                self.log_message("✅ 文字消息发送成功！")
                messagebox.showinfo("发送成功", "文字消息发送成功！")
                
        except Exception as e:
            self.log_message(f"❌ 发送文字消息时出错: {e}")
            messagebox.showerror("错误", f"发送文字消息时出错: {e}")
    
    def send_image_message(self):
        """发送图片消息"""
        if not self.recipient_var.get():
            messagebox.showerror("错误", "请输入接收者UID！")
            return
        
        if not self.selected_image_path:
            messagebox.showerror("错误", "请先选择要发送的图片！")
            return
        
        if not os.path.exists(self.selected_image_path):
            messagebox.showerror("错误", "选择的图片文件不存在！")
            return
        
        # 在新线程中执行发送操作
        threading.Thread(target=self._send_image_thread, daemon=True).start()
    
    def _send_image_thread(self):
        """在线程中发送图片消息"""
        try:
            self.log_message("🖼️ 开始发送图片消息...")
            
            if not self.create_sender():
                return
            
            result = self.sender.send_image(self.recipient_var.get(), self.selected_image_path)
            
            if 'error' in result:
                self.log_message(f"❌ 图片消息发送失败: {result['error']}")
                messagebox.showerror("发送失败", f"图片消息发送失败: {result['error']}")
            else:
                self.log_message("✅ 图片消息发送成功！")
                self.log_message(f"📋 文件令牌: {result.get('fileToken', 'N/A')}")
                messagebox.showinfo("发送成功", "图片消息发送成功！")
                
        except Exception as e:
            self.log_message(f"❌ 发送图片消息时出错: {e}")
            messagebox.showerror("错误", f"发送图片消息时出错: {e}")


def main():
    """主函数"""
    root = tk.Tk()

    # 设置窗口图标（如果有的话）
    try:
        root.iconbitmap('icon.ico')
    except:
        pass

    # 创建应用实例
    WeiboSenderGUI(root)

    # 启动GUI
    root.mainloop()


if __name__ == '__main__':
    main()
