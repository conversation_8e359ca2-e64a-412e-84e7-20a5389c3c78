#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
微博国际版粉丝监控工具
监控账号新增粉丝，自动发送欢迎消息
"""

import time
import json
import requests
import threading
import os
from typing import Set, List, Dict, Any, Optional
from datetime import datetime
from weibo_sender import WeiboSender


class WeiboFollowerMonitor:
    def __init__(self, uid: str, gsid: str, s_value: str, aid: str, sub: str, subp: str = None):
        """
        初始化粉丝监控器
        
        Args:
            uid: 你的微博UID
            gsid: GSID值
            s_value: S值
            aid: AID设备标识
            sub: SUB Cookie值
            subp: SUBP Cookie值（可选）
        """
        self.uid = uid
        self.gsid = gsid
        self.s_value = s_value
        self.aid = aid
        self.sub = sub
        self.subp = subp
        
        # 创建消息发送器
        self.sender = WeiboSender(uid, gsid, s_value, aid)
        self.sender.set_cookies(sub, subp)
        
        # 会话对象用于API请求
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'WeiboOverseas/6.7.9 (com.weibo.international; build:*******; iOS 18.5.0) Alamofire/5.10.2',
            'Accept': '*/*',
            'Accept-Language': 'zh-Hans-GB;q=1, zh-Hant-GB;q=0.9, yue-Hant-GB;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br'
        })
        
        # 设置Cookie
        cookies = {'SUB': sub}
        if subp:
            cookies['SUBP'] = subp
        self.session.cookies.update(cookies)
        
        # 监控状态
        self.is_monitoring = False
        self.monitor_thread = None
        self.known_followers: Set[str] = set()  # 已知粉丝UID集合
        self.check_interval = 60  # 检查间隔（秒）
        
        # 消息配置
        self.welcome_text = "感谢关注！欢迎来到我的微博！"
        self.welcome_image_path = None
        
        # 日志回调
        self.log_callback = None
    
    def set_log_callback(self, callback):
        """设置日志回调函数"""
        self.log_callback = callback
    
    def log(self, message: str):
        """记录日志"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_msg = f"[{timestamp}] {message}"
        print(log_msg)
        if self.log_callback:
            self.log_callback(log_msg)
    
    def set_welcome_message(self, text: str, image_path: str = None):
        """
        设置欢迎消息
        
        Args:
            text: 欢迎文字
            image_path: 欢迎图片路径（可选）
        """
        self.welcome_text = text
        self.welcome_image_path = image_path
        self.log(f"已设置欢迎消息: {text}")
        if image_path:
            self.log(f"已设置欢迎图片: {image_path}")
    
    def set_check_interval(self, seconds: int):
        """设置检查间隔"""
        self.check_interval = max(30, seconds)  # 最小30秒间隔
        self.log(f"检查间隔设置为: {self.check_interval}秒")
    
    def get_followers(self) -> Optional[List[Dict[str, Any]]]:
        """
        获取完整粉丝列表 - 使用移动端API逐页获取

        Returns:
            粉丝列表，失败返回None
        """
        all_followers = []
        page = 1
        max_pages = 10  # 限制最大10页

        self.log(f"🔍 开始获取完整粉丝列表...")

        while page <= max_pages:
            # 获取单页粉丝
            page_followers = self._get_followers_page(page)

            if page_followers is None:
                self.log(f"❌ 第{page}页获取失败，停止获取")
                break

            if len(page_followers) == 0:
                self.log(f"📄 第{page}页无数据，已获取完所有粉丝")
                break

            all_followers.extend(page_followers)
            self.log(f"📊 第{page}页获取成功: {len(page_followers)}个粉丝")

            # 如果这页用户数少于20，认为是最后一页
            if len(page_followers) < 20:
                self.log(f"📄 第{page}页用户数({len(page_followers)}) < 20，认为是最后一页")
                break

            page += 1

            # 页面间隔1秒，避免请求过快
            if page <= max_pages:
                time.sleep(1)

        self.log(f"✅ 获取完成，总计 {len(all_followers)} 个粉丝")
        return all_followers if all_followers else None

    def _get_followers_page(self, page: int = 1) -> Optional[List[Dict[str, Any]]]:
        """
        获取指定页的粉丝列表

        Args:
            page: 页码，从1开始

        Returns:
            粉丝列表，失败返回None
        """
        url = "https://api.weibo.cn/2/friendships/followers"

        params = {
            'uid': self.uid,
            'count': 20,  # 每页20个用户
            'page': page,
            'gsid': self.gsid,
            'aid': self.aid,
            's': self.s_value,
            'c': 'weicoabroad',
            'lang': 'zh_CN',
            'from': '12DC193010',
            'trim_status': 1,
            'ua': 'iPhone13,4_iOS18.5_Weibo_intl._6790_wifi__iphone__os18.5',
            'v_p': 59
        }

        try:
            response = self.session.get(url, params=params)

            if response.status_code == 200:
                data = response.json()

                if 'users' in data and isinstance(data['users'], list):
                    return data['users']
                else:
                    if isinstance(data, dict) and 'errmsg' in data:
                        self.log(f"❌ API错误: {data['errmsg']}")
                    return None
            else:
                self.log(f"❌ HTTP错误: {response.status_code}")
                return None

        except Exception as e:
            self.log(f"❌ 请求第{page}页失败: {e}")
            return None

    def _get_users_by_ids(self, user_ids: List[str]) -> Optional[List[Dict[str, Any]]]:
        """
        通过用户ID列表获取用户信息

        Args:
            user_ids: 用户ID列表

        Returns:
            用户信息列表
        """
        if not user_ids:
            return []

        # 批量获取用户信息API
        url = "https://api.weibo.com/2/users/show_batch.json"

        params = {
            'uids': ','.join(user_ids[:20]),  # 最多20个
            'source': '209678993',
            'gsid': self.gsid,
            's': self.s_value
        }

        try:
            response = self.session.get(url, params=params)
            response.raise_for_status()

            data = response.json()
            if isinstance(data, list):
                return data
            elif 'users' in data:
                return data['users']
            else:
                self.log(f"批量获取用户信息失败: {data}")
                return []

        except Exception as e:
            self.log(f"批量获取用户信息出错: {e}")
            return []
    
    def initialize_followers(self) -> bool:
        """
        初始化已知粉丝列表
        
        Returns:
            初始化是否成功
        """
        self.log("正在初始化粉丝列表...")
        followers = self.get_followers()
        
        if followers is None:
            self.log("❌ 初始化粉丝列表失败")
            return False
        
        # 记录所有已知粉丝的UID
        for follower in followers:
            follower_uid = str(follower.get('idstr', follower.get('id', '')))
            if follower_uid:
                self.known_followers.add(follower_uid)
        
        self.log(f"✅ 初始化完成，当前粉丝数: {len(self.known_followers)}")
        return True
    
    def check_new_followers(self) -> List[Dict[str, Any]]:
        """
        检查新增粉丝
        
        Returns:
            新增粉丝列表
        """
        followers = self.get_followers()
        if followers is None:
            return []
        
        new_followers = []
        current_follower_uids = set()
        
        for follower in followers:
            follower_uid = str(follower.get('idstr', follower.get('id', '')))
            if follower_uid:
                current_follower_uids.add(follower_uid)
                
                # 如果是新粉丝
                if follower_uid not in self.known_followers:
                    new_followers.append(follower)
                    self.known_followers.add(follower_uid)
        
        return new_followers
    
    def send_welcome_message(self, follower_uid: str, follower_name: str) -> bool:
        """
        发送欢迎消息给新粉丝
        
        Args:
            follower_uid: 粉丝UID
            follower_name: 粉丝昵称
            
        Returns:
            发送是否成功
        """
        success = True
        
        # 发送文字消息
        if self.welcome_text:
            self.log(f"📝 向 {follower_name} 发送欢迎文字...")
            text_result = self.sender.send_text(follower_uid, self.welcome_text)
            if 'error' in text_result:
                self.log(f"❌ 文字消息发送失败: {text_result['error']}")
                success = False
            else:
                self.log(f"✅ 文字消息发送成功")
        
        # 发送图片消息
        if self.welcome_image_path and os.path.exists(self.welcome_image_path):
            self.log(f"🖼️ 向 {follower_name} 发送欢迎图片...")
            image_result = self.sender.send_image(follower_uid, self.welcome_image_path)
            if 'error' in image_result:
                self.log(f"❌ 图片消息发送失败: {image_result['error']}")
                success = False
            else:
                self.log(f"✅ 图片消息发送成功")
        
        return success
    
    def monitor_loop(self):
        """监控循环"""
        self.log("🔍 开始监控新增粉丝...")
        
        while self.is_monitoring:
            try:
                # 检查新增粉丝
                new_followers = self.check_new_followers()
                
                if new_followers:
                    self.log(f"🎉 发现 {len(new_followers)} 个新粉丝!")
                    
                    for follower in new_followers:
                        follower_uid = str(follower.get('idstr', follower.get('id', '')))
                        follower_name = follower.get('screen_name', '未知用户')
                        
                        self.log(f"👤 新粉丝: {follower_name} (UID: {follower_uid})")
                        
                        # 发送欢迎消息
                        if self.send_welcome_message(follower_uid, follower_name):
                            self.log(f"✅ 已向 {follower_name} 发送欢迎消息")
                        else:
                            self.log(f"❌ 向 {follower_name} 发送欢迎消息失败")
                        
                        # 避免发送过快，间隔一下
                        time.sleep(2)
                
                else:
                    self.log(f"📊 检查完成，暂无新粉丝 (当前粉丝数: {len(self.known_followers)})")
                
                # 等待下次检查
                time.sleep(self.check_interval)
                
            except Exception as e:
                self.log(f"❌ 监控过程中出错: {e}")
                time.sleep(30)  # 出错后等待30秒再继续
    
    def start_monitoring(self) -> bool:
        """
        开始监控
        
        Returns:
            启动是否成功
        """
        if self.is_monitoring:
            self.log("⚠️ 监控已在运行中")
            return False
        
        # 初始化粉丝列表
        if not self.initialize_followers():
            return False
        
        # 启动监控线程
        self.is_monitoring = True
        self.monitor_thread = threading.Thread(target=self.monitor_loop, daemon=True)
        self.monitor_thread.start()
        
        self.log("🚀 粉丝监控已启动")
        return True
    
    def stop_monitoring(self):
        """停止监控"""
        if not self.is_monitoring:
            self.log("⚠️ 监控未在运行")
            return
        
        self.is_monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=5)
        
        self.log("🛑 粉丝监控已停止")
    
    def get_status(self) -> Dict[str, Any]:
        """获取监控状态"""
        return {
            'is_monitoring': self.is_monitoring,
            'follower_count': len(self.known_followers),
            'check_interval': self.check_interval,
            'welcome_text': self.welcome_text,
            'welcome_image': self.welcome_image_path is not None
        }


def main():
    """示例使用"""
    # 配置参数
    config = {
        'uid': 'YOUR_UID',
        'gsid': 'YOUR_GSID',
        's_value': 'dddddddd',
        'aid': 'YOUR_AID',
        'sub': 'YOUR_SUB_COOKIE',
        'subp': None  # 可选
    }
    
    # 创建监控器
    monitor = WeiboFollowerMonitor(
        uid=config['uid'],
        gsid=config['gsid'],
        s_value=config['s_value'],
        aid=config['aid'],
        sub=config['sub'],
        subp=config.get('subp')
    )
    
    # 设置欢迎消息
    monitor.set_welcome_message(
        text="感谢关注！欢迎来到我的微博！🎉",
        image_path="welcome.jpg"  # 可选的欢迎图片
    )
    
    # 设置检查间隔（60秒）
    monitor.set_check_interval(60)
    
    # 开始监控
    if monitor.start_monitoring():
        try:
            # 保持程序运行
            while True:
                time.sleep(10)
                status = monitor.get_status()
                print(f"监控状态: 运行中, 粉丝数: {status['follower_count']}")
        except KeyboardInterrupt:
            print("\n收到停止信号...")
            monitor.stop_monitoring()
    else:
        print("启动监控失败")


if __name__ == '__main__':
    main()
