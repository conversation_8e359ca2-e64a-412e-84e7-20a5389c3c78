#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
微博国际版粉丝监控工具 - 批量监控版本
支持批量导入账号，自动计算参数，表格化显示监控结果
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext, font
import threading
import time
import requests
import json
import os
import hashlib
from typing import Dict, List, Any, Optional


class AccountInfo:
    """账号信息类"""
    def __init__(self, uid: str, gsid: str):
        self.uid = uid
        self.gsid = gsid
        self.aid = ""
        self.s_value = ""
        self.nickname = ""
        self.initial_followers = 0
        self.current_followers = 0
        self.growth = 0
        self.status = "待处理"
        self.cached_followers = set()
    
    def get_growth(self):
        """计算增长数量"""
        self.growth = self.current_followers - self.initial_followers
        return self.growth


class WeiboBatchMonitorGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("微博国际版粉丝监控工具")
        self.root.geometry("1200x800")
        self.root.resizable(True, True)
        
        # 设置样式
        style = ttk.Style()
        style.theme_use('clam')
        
        # 账号列表
        self.accounts: List[AccountInfo] = []
        self.is_monitoring = False
        self.monitor_thread = None
        self.welcome_text = "感谢关注！欢迎来到我的微博！🎉"
        self.welcome_image_path = None
        self.check_interval = 30

        # 欢迎语列表 - 每个元素是字典 {'type': 'text'/'image', 'content': '内容', 'id': 唯一ID}
        self.welcome_messages = [
            {'type': 'text', 'content': '感谢关注！欢迎来到我的微博！🎉', 'id': 1}
        ]
        self.next_welcome_id = 2

        # 代理设置
        self.use_proxy = False
        self.proxy_api_url = ""
        self.proxy_refresh_interval = 1  # 分钟
        self.current_proxy = None
        self.proxy_last_update = 0
        self.proxy_thread = None

        # 多线程管理
        import queue
        self.message_queue = queue.Queue()  # 消息发送队列
        self.message_thread = None  # 消息发送线程
        self.thread_lock = threading.Lock()  # 线程锁

        # 配置文件
        self.config_file = "weibo_monitor_config.json"

        # 设置窗口样式
        self.setup_window()

        self.create_widgets()

        # 加载配置（在界面创建后）
        self.load_config()

    def setup_window(self):
        """设置窗口样式"""
        self.root.title("🚀 微博国际版粉丝监控工具 - 专业版")
        self.root.geometry("900x900")
        self.root.configure(bg='#f8f9fa')

        # 设置窗口图标（如果有的话）
        try:
            # self.root.iconbitmap('icon.ico')  # 可以添加图标文件
            pass
        except:
            pass

        # 设置现代化样式
        self.setup_styles()

    def setup_styles(self):
        """设置现代化UI样式"""
        style = ttk.Style()

        # 设置主题
        try:
            style.theme_use('clam')  # 使用现代主题
        except:
            pass

        # 自定义样式
        # 主框架样式
        style.configure('Main.TFrame',
                       background='#f8f9fa',
                       relief='flat')

        # 卡片样式框架
        style.configure('Card.TFrame',
                       background='#ffffff',
                       relief='solid',
                       borderwidth=1)

        # 标题标签样式
        style.configure('Title.TLabel',
                       background='#ffffff',
                       foreground='#2c3e50',
                       font=('Microsoft YaHei UI', 12, 'bold'))

        # 子标题样式
        style.configure('Subtitle.TLabel',
                       background='#ffffff',
                       foreground='#34495e',
                       font=('Microsoft YaHei UI', 10))

        # 状态标签样式
        style.configure('Status.TLabel',
                       background='#f8f9fa',
                       foreground='#27ae60',
                       font=('Microsoft YaHei UI', 10, 'bold'))

        # 按钮样式
        style.configure('Action.TButton',
                       font=('Microsoft YaHei UI', 9),
                       padding=(10, 5))

        # 主要按钮样式
        style.configure('Primary.TButton',
                       font=('Microsoft YaHei UI', 9, 'bold'),
                       padding=(15, 8))

        # 表格样式
        style.configure('Modern.Treeview',
                       background='#ffffff',
                       foreground='#2c3e50',
                       fieldbackground='#ffffff',
                       font=('Microsoft YaHei UI', 9))

        style.configure('Modern.Treeview.Heading',
                       background='#ecf0f1',
                       foreground='#2c3e50',
                       font=('Microsoft YaHei UI', 9, 'bold'))

    def create_widgets(self):
        """创建界面组件"""
        # 创建主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(0, weight=1)
        main_frame.rowconfigure(1, weight=1)
        
        # 顶部按钮区域
        top_button_frame = tk.Frame(main_frame)
        top_button_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(5, 10))

        ttk.Button(top_button_frame, text="📁 选择文件", command=self.import_from_file).pack(side=tk.LEFT, padx=5)
        ttk.Button(top_button_frame, text="📋 从剪贴板", command=self.import_from_clipboard).pack(side=tk.LEFT, padx=5)
        ttk.Button(top_button_frame, text="🗑️ 清空账号", command=self.clear_accounts).pack(side=tk.LEFT, padx=5)
        
        # 账号导入区域 - 简化版
        import_frame = ttk.LabelFrame(main_frame, text="账号导入", padding="10")
        import_frame.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        import_frame.columnconfigure(0, weight=1)



        # 手动导入按钮
        button_frame = tk.Frame(import_frame)
        button_frame.grid(row=1, column=0, pady=5)
        ttk.Button(button_frame, text="� 选择文件", command=self.import_from_file).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="📋 从剪贴板", command=self.import_from_clipboard).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="🗑️ 清空账号", command=self.clear_accounts).pack(side=tk.LEFT, padx=5)
        
        # 账号列表表格
        table_frame = ttk.LabelFrame(main_frame, text="账号监控列表", padding="10")
        table_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        table_frame.columnconfigure(0, weight=1)
        table_frame.rowconfigure(0, weight=1)
        main_frame.rowconfigure(1, weight=2)
        
        # 创建表格
        columns = ("序号", "UID", "昵称", "初始粉丝数", "现有粉丝数", "增长数量", "状态")
        self.tree = ttk.Treeview(table_frame, columns=columns, show="headings", height=10)
        
        # 设置列标题和宽度
        for col in columns:
            self.tree.heading(col, text=col)
            if col == "序号":
                self.tree.column(col, width=50, anchor="center")
            elif col == "UID":
                self.tree.column(col, width=120, anchor="center")
            elif col == "昵称":
                self.tree.column(col, width=150, anchor="w")
            elif col in ["初始粉丝数", "现有粉丝数", "增长数量"]:
                self.tree.column(col, width=100, anchor="center")
            else:
                self.tree.column(col, width=120, anchor="center")
        
        # 添加滚动条
        scrollbar = ttk.Scrollbar(table_frame, orient="vertical", command=self.tree.yview)
        self.tree.configure(yscrollcommand=scrollbar.set)
        
        self.tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        
        # 监控设置区域
        settings_frame = ttk.LabelFrame(main_frame, text="监控设置", padding="10")
        settings_frame.grid(row=2, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        settings_frame.columnconfigure(2, weight=1)
        
        # 检查间隔
        ttk.Label(settings_frame, text="检查间隔(秒):").grid(row=0, column=0, sticky=tk.W, pady=2)
        self.interval_var = tk.StringVar(value="30")
        ttk.Entry(settings_frame, textvariable=self.interval_var, width=10).grid(row=0, column=1, sticky=tk.W, padx=(10, 20), pady=2)

        # 欢迎语管理
        ttk.Label(settings_frame, text="欢迎语管理:").grid(row=0, column=2, sticky=tk.W, pady=2)
        ttk.Button(settings_frame, text="📝 管理欢迎语", command=self.open_welcome_manager).grid(row=0, column=3, sticky=tk.W, padx=(10, 0), pady=2)

        # 代理设置
        ttk.Label(settings_frame, text="代理设置:").grid(row=0, column=4, sticky=tk.W, pady=2, padx=(20, 0))
        ttk.Button(settings_frame, text="🌐 代理设置", command=self.open_proxy_settings).grid(row=0, column=5, sticky=tk.W, padx=(10, 0), pady=2)

        # 显示当前欢迎语数量和代理状态
        self.welcome_count_var = tk.StringVar(value=f"当前有 {len(self.welcome_messages)} 条欢迎语")
        ttk.Label(settings_frame, textvariable=self.welcome_count_var, foreground="gray").grid(row=1, column=2, columnspan=2, sticky=tk.W, padx=(0, 0), pady=2)

        self.proxy_status_var = tk.StringVar(value="代理: 未启用")
        ttk.Label(settings_frame, textvariable=self.proxy_status_var, foreground="gray").grid(row=1, column=4, columnspan=2, sticky=tk.W, padx=(20, 0), pady=2)
        
        # 控制按钮区域
        control_frame = ttk.Frame(main_frame)
        control_frame.grid(row=3, column=0, pady=(0, 10))
        
        self.start_button = ttk.Button(control_frame, text="🚀 开始监控", command=self.start_monitoring)
        self.start_button.grid(row=0, column=0, padx=(0, 10))
        
        self.stop_button = ttk.Button(control_frame, text="🛑 停止监控", command=self.stop_monitoring, state='disabled')
        self.stop_button.grid(row=0, column=1, padx=(0, 10))
        
        ttk.Button(control_frame, text="🔄 刷新状态", command=self.refresh_status).grid(row=0, column=2, padx=(0, 10))

        # 重发私信功能
        resend_frame = ttk.Frame(control_frame)
        resend_frame.grid(row=0, column=3, padx=(20, 10))

        ttk.Label(resend_frame, text="给前").grid(row=0, column=0)
        self.resend_count_var = tk.StringVar(value="40")
        resend_count_entry = ttk.Entry(resend_frame, textvariable=self.resend_count_var, width=5)
        resend_count_entry.grid(row=0, column=1, padx=2)
        ttk.Label(resend_frame, text="名粉丝重发").grid(row=0, column=2)

        self.resend_button = ttk.Button(resend_frame, text="🔄 重发私信", command=self.start_resend_messages)
        self.resend_button.grid(row=0, column=3, padx=(5, 0))

        # 状态显示
        self.status_var = tk.StringVar(value="状态: 未启动")
        ttk.Label(control_frame, textvariable=self.status_var, font=('Arial', 10, 'bold')).grid(row=0, column=4, padx=(20, 0))
        
        # 日志区域
        log_frame = ttk.LabelFrame(main_frame, text="监控日志", padding="10")
        log_frame.grid(row=4, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
        main_frame.rowconfigure(4, weight=1)
        
        self.log_text = scrolledtext.ScrolledText(log_frame, height=8, width=100)
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 清空日志按钮
        ttk.Button(log_frame, text="清空日志", command=self.clear_log).grid(row=1, column=0, sticky=tk.E, pady=(5, 0))
    
    def log_message(self, message):
        """添加日志消息"""
        timestamp = time.strftime("%H:%M:%S")
        log_msg = f"[{timestamp}] {message}"
        self.log_text.insert(tk.END, f"{log_msg}\n")
        self.log_text.see(tk.END)
        self.root.update_idletasks()
    
    def clear_log(self):
        """清空日志"""
        self.log_text.delete(1.0, tk.END)
    
    def import_from_clipboard(self):
        """从剪贴板导入账号"""
        try:
            clipboard_text = self.root.clipboard_get()
            if clipboard_text:
                self.account_input.delete(1.0, tk.END)
                self.account_input.insert(1.0, clipboard_text.strip())
                self.log_message("📋 已从剪贴板导入账号信息")
            else:
                messagebox.showwarning("警告", "剪贴板为空")
        except Exception as e:
            messagebox.showerror("错误", f"读取剪贴板失败: {e}")
    
    def parse_accounts(self):
        """解析账号信息"""
        account_text = self.account_input.get(1.0, tk.END).strip()
        
        if not account_text:
            messagebox.showerror("错误", "请输入账号信息")
            return
        
        lines = [line.strip() for line in account_text.split('\n') if line.strip()]
        
        if not lines:
            messagebox.showerror("错误", "没有有效的账号信息")
            return
        
        # 清空现有账号列表
        self.accounts.clear()
        for item in self.tree.get_children():
            self.tree.delete(item)
        
        # 解析每行账号
        valid_accounts = 0
        for i, line in enumerate(lines, 1):
            if '----' not in line:
                self.log_message(f"❌ 第{i}行格式错误，跳过: {line[:50]}...")
                continue
            
            try:
                uid, gsid = line.split('----', 1)
                if not uid or not gsid:
                    self.log_message(f"❌ 第{i}行UID或GSID为空，跳过")
                    continue
                
                account = AccountInfo(uid.strip(), gsid.strip())
                self.accounts.append(account)
                
                # 添加到表格
                self.tree.insert("", "end", values=(
                    len(self.accounts),
                    account.uid,
                    account.nickname or "获取中...",
                    account.initial_followers,
                    account.current_followers,
                    account.growth,
                    account.status
                ))
                
                valid_accounts += 1
                
            except Exception as e:
                self.log_message(f"❌ 第{i}行解析失败: {e}")
                continue
        
        if valid_accounts > 0:
            self.log_message(f"✅ 成功解析 {valid_accounts} 个账号")
            # 开始获取账号详细信息
            threading.Thread(target=self.fetch_account_details, daemon=True).start()
        else:
            messagebox.showerror("错误", "没有成功解析任何账号")
    
    def select_welcome_image(self):
        """选择欢迎图片"""
        file_path = filedialog.askopenfilename(
            title="选择欢迎图片",
            filetypes=[
                ("图片文件", "*.jpg *.jpeg *.png *.gif *.bmp *.webp"),
                ("所有文件", "*.*")
            ]
        )

        if file_path:
            self.welcome_image_path = file_path
            filename = os.path.basename(file_path)
            self.image_path_var.set(f"已选择: {filename}")
            self.log_message(f"📁 已选择欢迎图片: {filename}")

    def open_welcome_manager(self):
        """打开欢迎语管理窗口"""
        # 创建新窗口
        welcome_window = tk.Toplevel(self.root)
        welcome_window.title("欢迎语管理")
        welcome_window.geometry("800x600")
        welcome_window.transient(self.root)
        welcome_window.grab_set()

        # 主框架
        main_frame = ttk.Frame(welcome_window, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        welcome_window.columnconfigure(0, weight=1)
        welcome_window.rowconfigure(0, weight=1)
        main_frame.columnconfigure(0, weight=1)
        main_frame.rowconfigure(1, weight=1)

        # 标题和新增按钮
        title_frame = ttk.Frame(main_frame)
        title_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))

        ttk.Label(title_frame, text="欢迎语管理", font=('Arial', 14, 'bold')).pack(side=tk.LEFT)
        ttk.Button(title_frame, text="➕ 新增欢迎语", command=lambda: self.add_welcome_message(welcome_listbox)).pack(side=tk.RIGHT)

        # 欢迎语列表
        list_frame = ttk.LabelFrame(main_frame, text="欢迎语列表", padding="10")
        list_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        list_frame.columnconfigure(0, weight=1)
        list_frame.rowconfigure(0, weight=1)

        # 创建列表框和滚动条
        welcome_listbox = tk.Listbox(list_frame, height=15)
        scrollbar = ttk.Scrollbar(list_frame, orient="vertical", command=welcome_listbox.yview)
        welcome_listbox.configure(yscrollcommand=scrollbar.set)

        welcome_listbox.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))

        # 操作按钮
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=2, column=0, pady=(10, 0))

        ttk.Button(button_frame, text="✏️ 修改", command=lambda: self.edit_welcome_message(welcome_listbox)).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="🗑️ 删除", command=lambda: self.delete_welcome_message(welcome_listbox)).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="📋 预览", command=lambda: self.preview_welcome_message(welcome_listbox)).pack(side=tk.LEFT, padx=5)

        # 关闭按钮
        ttk.Button(button_frame, text="关闭", command=welcome_window.destroy).pack(side=tk.RIGHT, padx=5)

        # 刷新列表
        self.refresh_welcome_list(welcome_listbox)

    def refresh_welcome_list(self, listbox):
        """刷新欢迎语列表"""
        listbox.delete(0, tk.END)
        for i, msg in enumerate(self.welcome_messages):
            msg_type = "📝 文字" if msg['type'] == 'text' else "🖼️ 图片"
            content_preview = msg['content'][:50] + "..." if len(msg['content']) > 50 else msg['content']
            listbox.insert(tk.END, f"欢迎语{i+1} - {msg_type}: {content_preview}")

        # 更新主界面的欢迎语数量显示
        self.welcome_count_var.set(f"当前有 {len(self.welcome_messages)} 条欢迎语")

    def add_welcome_message(self, listbox):
        """新增欢迎语"""
        self.edit_welcome_dialog(listbox, None)

    def edit_welcome_message(self, listbox):
        """修改欢迎语"""
        selection = listbox.curselection()
        if not selection:
            messagebox.showwarning("提示", "请先选择要修改的欢迎语")
            return

        index = selection[0]
        self.edit_welcome_dialog(listbox, index)

    def delete_welcome_message(self, listbox):
        """删除欢迎语"""
        selection = listbox.curselection()
        if not selection:
            messagebox.showwarning("提示", "请先选择要删除的欢迎语")
            return

        if len(self.welcome_messages) <= 1:
            messagebox.showwarning("提示", "至少需要保留一条欢迎语")
            return

        index = selection[0]
        msg = self.welcome_messages[index]
        msg_type = "文字" if msg['type'] == 'text' else "图片"
        content_preview = msg['content'][:30] + "..." if len(msg['content']) > 30 else msg['content']

        result = messagebox.askyesno("确认删除", f"确定要删除这条{msg_type}欢迎语吗？\n\n{content_preview}")
        if result:
            del self.welcome_messages[index]
            self.refresh_welcome_list(listbox)

            # 自动保存配置
            self.save_config()

            self.log_message(f"🗑️ 已删除欢迎语: {content_preview}")

    def preview_welcome_message(self, listbox):
        """预览欢迎语"""
        selection = listbox.curselection()
        if not selection:
            messagebox.showwarning("提示", "请先选择要预览的欢迎语")
            return

        index = selection[0]
        msg = self.welcome_messages[index]

        if msg['type'] == 'text':
            messagebox.showinfo("欢迎语预览", f"文字内容：\n\n{msg['content']}")
        else:
            # 图片预览
            try:
                import os
                if os.path.exists(msg['content']):
                    messagebox.showinfo("欢迎语预览", f"图片路径：\n{msg['content']}\n\n图片文件存在，发送时将使用此图片。")
                else:
                    messagebox.showwarning("欢迎语预览", f"图片路径：\n{msg['content']}\n\n⚠️ 图片文件不存在，请重新选择图片。")
            except Exception as e:
                messagebox.showerror("预览错误", f"预览图片时出错：{e}")

    def edit_welcome_dialog(self, listbox, edit_index=None):
        """编辑欢迎语对话框"""
        # 创建对话框窗口
        dialog = tk.Toplevel(self.root)
        dialog.title("新增欢迎语" if edit_index is None else "修改欢迎语")
        dialog.geometry("500x400")
        dialog.transient(self.root)
        dialog.grab_set()

        # 如果是修改模式，获取现有数据
        if edit_index is not None:
            existing_msg = self.welcome_messages[edit_index]
            default_type = existing_msg['type']
            default_content = existing_msg['content']
        else:
            default_type = 'text'
            default_content = ''

        # 主框架
        main_frame = ttk.Frame(dialog, padding="20")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        dialog.columnconfigure(0, weight=1)
        dialog.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)

        # 欢迎语类型选择
        ttk.Label(main_frame, text="欢迎语类型:", font=('Arial', 10, 'bold')).grid(row=0, column=0, sticky=tk.W, pady=(0, 10))

        type_var = tk.StringVar(value=default_type)
        type_frame = ttk.Frame(main_frame)
        type_frame.grid(row=0, column=1, sticky=(tk.W, tk.E), pady=(0, 10))

        def on_type_change():
            self.toggle_content_input(type_var.get(), content_frame, content_var, file_path_var,
                                     text_frame, text_label, text_entry, image_frame, image_label, image_path_label, image_button)

        ttk.Radiobutton(type_frame, text="📝 文字", variable=type_var, value='text',
                       command=on_type_change).pack(side=tk.LEFT, padx=(0, 20))
        ttk.Radiobutton(type_frame, text="🖼️ 图片", variable=type_var, value='image',
                       command=on_type_change).pack(side=tk.LEFT)

        # 内容输入区域
        ttk.Label(main_frame, text="内容:", font=('Arial', 10, 'bold')).grid(row=1, column=0, sticky=(tk.W, tk.N), pady=(0, 10))

        content_frame = ttk.Frame(main_frame)
        content_frame.grid(row=1, column=1, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        content_frame.columnconfigure(0, weight=1)
        content_frame.rowconfigure(0, weight=1)

        # 文字输入框
        content_var = tk.StringVar(value=default_content if default_type == 'text' else '')
        text_frame = ttk.Frame(content_frame)
        text_label = ttk.Label(text_frame, text="请输入欢迎文字:")
        text_entry = tk.Text(text_frame, height=8, width=40, wrap=tk.WORD)
        if default_type == 'text':
            text_entry.insert('1.0', default_content)

        # 图片选择框
        file_path_var = tk.StringVar(value=default_content if default_type == 'image' else '')
        image_frame = ttk.Frame(content_frame)
        image_label = ttk.Label(image_frame, text="请选择图片文件:")
        image_path_label = ttk.Label(image_frame, textvariable=file_path_var, foreground="gray", wraplength=300)
        image_button = ttk.Button(image_frame, text="选择图片",
                                 command=lambda: self.select_image_file(file_path_var))

        # 根据当前类型显示对应的输入控件
        self.toggle_content_input(default_type, content_frame, content_var, file_path_var,
                                 text_frame, text_label, text_entry, image_frame, image_label, image_path_label, image_button)

        # 按钮区域
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=2, column=0, columnspan=2, pady=(20, 0))

        def save_welcome():
            msg_type = type_var.get()
            if msg_type == 'text':
                content = text_entry.get('1.0', tk.END).strip()
                if not content:
                    messagebox.showwarning("提示", "请输入欢迎文字")
                    return
            else:
                content = file_path_var.get().strip()
                if not content:
                    messagebox.showwarning("提示", "请选择图片文件")
                    return
                if not os.path.exists(content):
                    messagebox.showwarning("提示", "选择的图片文件不存在")
                    return

            # 保存或更新欢迎语
            if edit_index is None:
                # 新增
                new_msg = {
                    'type': msg_type,
                    'content': content,
                    'id': self.next_welcome_id
                }
                self.welcome_messages.append(new_msg)
                self.next_welcome_id += 1
                self.log_message(f"➕ 已新增{('文字' if msg_type == 'text' else '图片')}欢迎语")
            else:
                # 修改
                self.welcome_messages[edit_index] = {
                    'type': msg_type,
                    'content': content,
                    'id': self.welcome_messages[edit_index]['id']
                }
                self.log_message(f"✏️ 已修改{('文字' if msg_type == 'text' else '图片')}欢迎语")

            self.refresh_welcome_list(listbox)

            # 自动保存配置
            self.save_config()

            dialog.destroy()

        ttk.Button(button_frame, text="保存", command=save_welcome).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="取消", command=dialog.destroy).pack(side=tk.LEFT, padx=5)

        # 存储控件引用以便切换显示
        dialog.text_frame = text_frame
        dialog.text_label = text_label
        dialog.text_entry = text_entry
        dialog.image_frame = image_frame
        dialog.image_label = image_label
        dialog.image_path_label = image_path_label
        dialog.image_button = image_button

    def toggle_content_input(self, msg_type, content_frame, content_var, file_path_var,
                           text_frame=None, text_label=None, text_entry=None,
                           image_frame=None, image_label=None, image_path_label=None, image_button=None):
        """切换内容输入控件显示"""
        # 隐藏所有控件
        for widget in content_frame.winfo_children():
            widget.grid_forget()

        if msg_type == 'text':
            # 显示文字输入控件
            if text_frame:
                text_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
                text_label.grid(row=0, column=0, sticky=tk.W, pady=(0, 5))
                text_entry.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
                text_frame.columnconfigure(0, weight=1)
                text_frame.rowconfigure(1, weight=1)
        else:
            # 显示图片选择控件
            if image_frame:
                image_frame.grid(row=0, column=0, sticky=(tk.W, tk.E))
                image_label.grid(row=0, column=0, sticky=tk.W, pady=(0, 5))
                image_path_label.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(0, 5))
                image_button.grid(row=2, column=0, sticky=tk.W)
                image_frame.columnconfigure(0, weight=1)

    def select_image_file(self, file_path_var):
        """选择图片文件"""
        file_path = filedialog.askopenfilename(
            title="选择欢迎图片",
            filetypes=[
                ("图片文件", "*.jpg *.jpeg *.png *.gif *.bmp *.webp"),
                ("所有文件", "*.*")
            ]
        )

        if file_path:
            file_path_var.set(file_path)
            filename = os.path.basename(file_path)
            self.log_message(f"📁 已选择图片: {filename}")

    def open_proxy_settings(self):
        """打开代理设置窗口"""
        # 创建代理设置窗口
        proxy_window = tk.Toplevel(self.root)
        proxy_window.title("代理设置")
        proxy_window.geometry("600x500")
        proxy_window.transient(self.root)
        proxy_window.grab_set()

        # 主框架
        main_frame = ttk.Frame(proxy_window, padding="20")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        proxy_window.columnconfigure(0, weight=1)
        proxy_window.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)

        # 标题
        ttk.Label(main_frame, text="代理设置", font=('Arial', 14, 'bold')).grid(row=0, column=0, columnspan=2, pady=(0, 20))

        # 启用代理选项
        self.proxy_enabled_var = tk.BooleanVar(value=self.use_proxy)
        ttk.Checkbutton(main_frame, text="启用代理", variable=self.proxy_enabled_var,
                       command=lambda: self.toggle_proxy_controls(proxy_controls_frame)).grid(row=1, column=0, columnspan=2, sticky=tk.W, pady=(0, 15))

        # 代理控制区域
        proxy_controls_frame = ttk.LabelFrame(main_frame, text="代理配置", padding="15")
        proxy_controls_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 15))
        proxy_controls_frame.columnconfigure(1, weight=1)

        # API地址
        ttk.Label(proxy_controls_frame, text="代理API地址:").grid(row=0, column=0, sticky=tk.W, pady=(0, 10))
        self.proxy_api_var = tk.StringVar(value=self.proxy_api_url)
        api_entry = ttk.Entry(proxy_controls_frame, textvariable=self.proxy_api_var, width=60)
        api_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(10, 0), pady=(0, 10))

        # API示例
        example_text = "示例: https://service.ipzan.com/core-extract?num=1&no=xxx&minute=1&area=410000&repeat=1&protocol=1&pool=quality&mode=auth&secret=xxx"
        ttk.Label(proxy_controls_frame, text=example_text, foreground="gray", wraplength=500).grid(row=1, column=0, columnspan=2, sticky=tk.W, pady=(0, 15))

        # 刷新间隔
        ttk.Label(proxy_controls_frame, text="刷新间隔(分钟):").grid(row=2, column=0, sticky=tk.W, pady=(0, 10))
        self.proxy_interval_var = tk.StringVar(value=str(self.proxy_refresh_interval))
        ttk.Entry(proxy_controls_frame, textvariable=self.proxy_interval_var, width=10).grid(row=2, column=1, sticky=tk.W, padx=(10, 0), pady=(0, 10))

        # 当前代理状态
        status_frame = ttk.LabelFrame(proxy_controls_frame, text="当前代理状态", padding="10")
        status_frame.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(15, 0))
        status_frame.columnconfigure(0, weight=1)

        self.current_proxy_var = tk.StringVar(value=self.get_proxy_status_text())
        ttk.Label(status_frame, textvariable=self.current_proxy_var, foreground="blue").grid(row=0, column=0, sticky=tk.W)

        # 测试按钮
        ttk.Button(status_frame, text="🔄 测试代理", command=lambda: self.test_proxy_connection(self.current_proxy_var)).grid(row=0, column=1, padx=(10, 0))
        ttk.Button(status_frame, text="🆕 获取新代理", command=lambda: self.fetch_new_proxy(self.current_proxy_var)).grid(row=0, column=2, padx=(5, 0))

        # 按钮区域
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=3, column=0, columnspan=2, pady=(20, 0))

        def save_proxy_settings():
            try:
                self.use_proxy = self.proxy_enabled_var.get()
                self.proxy_api_url = self.proxy_api_var.get().strip()
                self.proxy_refresh_interval = int(self.proxy_interval_var.get())

                if self.proxy_refresh_interval < 1:
                    messagebox.showwarning("提示", "刷新间隔不能少于1分钟")
                    return

                # 更新主界面状态显示
                self.update_proxy_status_display()

                # 如果启用代理且没有当前代理，获取一个
                if self.use_proxy and not self.current_proxy:
                    self.fetch_new_proxy()

                # 自动保存配置
                self.save_config()

                self.log_message(f"🌐 代理设置已保存: {'启用' if self.use_proxy else '禁用'}")
                proxy_window.destroy()

            except ValueError:
                messagebox.showerror("错误", "刷新间隔必须是数字")

        ttk.Button(button_frame, text="保存", command=save_proxy_settings).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="取消", command=proxy_window.destroy).pack(side=tk.LEFT, padx=5)

        # 初始化控件状态
        self.toggle_proxy_controls(proxy_controls_frame)

    def toggle_proxy_controls(self, controls_frame):
        """切换代理控件的启用状态"""
        enabled = self.proxy_enabled_var.get()
        state = 'normal' if enabled else 'disabled'

        for child in controls_frame.winfo_children():
            if isinstance(child, (ttk.Entry, ttk.Button)):
                child.config(state=state)
            elif isinstance(child, ttk.LabelFrame):
                for subchild in child.winfo_children():
                    if isinstance(subchild, (ttk.Entry, ttk.Button)):
                        subchild.config(state=state)

    def get_proxy_status_text(self):
        """获取代理状态文本"""
        if not self.use_proxy:
            return "代理未启用"
        elif not self.current_proxy:
            return "未获取到代理"
        else:
            return f"当前代理: {self.current_proxy['ip']}:{self.current_proxy['port']}"

    def update_proxy_status_display(self):
        """更新主界面的代理状态显示"""
        if self.use_proxy:
            if self.current_proxy:
                self.proxy_status_var.set(f"代理: {self.current_proxy['ip']}:{self.current_proxy['port']}")
            else:
                self.proxy_status_var.set("代理: 获取中...")
        else:
            self.proxy_status_var.set("代理: 未启用")

    def fetch_new_proxy(self, status_var=None):
        """获取新的代理IP"""
        if not self.proxy_api_url:
            self.log_message("❌ 代理API地址未设置")
            return False

        try:
            self.log_message("🔄 正在获取新代理...")
            response = requests.get(self.proxy_api_url, timeout=10)

            if response.status_code == 200:
                proxy_text = response.text.strip()
                self.log_message(f"📡 代理API响应: {proxy_text}")

                # 解析代理信息: IP:端口 用户名 密码
                parts = proxy_text.split()
                if len(parts) >= 3:
                    ip_port = parts[0]
                    username = parts[1]
                    password = parts[2]

                    if ':' in ip_port:
                        ip, port = ip_port.split(':', 1)

                        self.current_proxy = {
                            'ip': ip,
                            'port': port,
                            'username': username,
                            'password': password
                        }

                        self.proxy_last_update = time.time()

                        self.log_message(f"✅ 获取新代理成功: {ip}:{port}")

                        # 更新状态显示
                        self.update_proxy_status_display()
                        if status_var:
                            status_var.set(self.get_proxy_status_text())

                        return True
                    else:
                        self.log_message("❌ 代理格式错误: IP端口格式不正确")
                else:
                    self.log_message("❌ 代理格式错误: 缺少必要信息")
            else:
                self.log_message(f"❌ 获取代理失败: HTTP {response.status_code}")

        except Exception as e:
            self.log_message(f"❌ 获取代理异常: {e}")

        return False

    def test_proxy_connection(self, status_var=None):
        """测试代理连接"""
        if not self.current_proxy:
            messagebox.showwarning("提示", "当前没有代理可测试")
            return

        try:
            proxy_dict = self.get_proxy_dict()
            self.log_message("🔍 正在测试代理连接...")

            # 测试连接到百度
            response = requests.get("https://www.baidu.com",
                                  proxies=proxy_dict,
                                  timeout=10)

            if response.status_code == 200:
                self.log_message("✅ 代理连接测试成功")
                messagebox.showinfo("测试结果", "代理连接正常")
            else:
                self.log_message(f"❌ 代理连接测试失败: HTTP {response.status_code}")
                messagebox.showwarning("测试结果", f"代理连接异常: HTTP {response.status_code}")

        except Exception as e:
            self.log_message(f"❌ 代理连接测试异常: {e}")
            messagebox.showerror("测试结果", f"代理连接失败: {e}")

    def get_proxy_dict(self):
        """获取requests使用的代理字典"""
        if not self.current_proxy:
            return None

        proxy_url = f"http://{self.current_proxy['username']}:{self.current_proxy['password']}@{self.current_proxy['ip']}:{self.current_proxy['port']}"
        return {
            'http': proxy_url,
            'https': proxy_url
        }

    def should_refresh_proxy(self):
        """检查是否需要刷新代理"""
        if not self.use_proxy or not self.current_proxy:
            return False

        elapsed_minutes = (time.time() - self.proxy_last_update) / 60
        return elapsed_minutes >= self.proxy_refresh_interval

    def auto_refresh_proxy(self):
        """自动刷新代理"""
        if self.should_refresh_proxy():
            self.log_message("⏰ 代理已过期，正在获取新代理...")
            self.fetch_new_proxy()

    def import_from_file(self):
        """从文件导入账号"""
        try:
            file_path = filedialog.askopenfilename(
                title="选择账号文件",
                filetypes=[("文本文件", "*.txt"), ("所有文件", "*.*")]
            )

            if file_path:
                self.load_accounts_from_file(file_path)

        except Exception as e:
            messagebox.showerror("错误", f"导入文件失败: {e}")
            self.log_message(f"❌ 导入文件失败: {e}")

    def load_accounts_from_file(self, file_path: str):
        """从文件加载账号"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read().strip()

            if not content:
                messagebox.showwarning("警告", "文件内容为空")
                return

            lines = [line.strip() for line in content.split('\n') if line.strip()]
            new_accounts = []

            for line_num, line in enumerate(lines, 1):
                # 解析格式: UID----GSID
                if '----' in line:
                    parts = line.split('----', 1)
                    if len(parts) >= 2:
                        uid = parts[0].strip()
                        gsid = parts[1].strip()

                        if uid and gsid:
                            account = AccountInfo(uid, gsid)
                            new_accounts.append(account)
                        else:
                            self.log_message(f"⚠️ 第{line_num}行UID或GSID为空: {line}")
                    else:
                        self.log_message(f"⚠️ 第{line_num}行格式错误: {line}")
                else:
                    self.log_message(f"⚠️ 第{line_num}行缺少'----'分隔符: {line}")

            if new_accounts:
                # 清空现有账号
                self.accounts.clear()
                for item in self.tree.get_children():
                    self.tree.delete(item)

                # 添加新账号
                self.accounts.extend(new_accounts)
                self.update_account_table()
                self.log_message(f"✅ 成功从文件导入 {len(new_accounts)} 个账号")

                # 开始获取账号详细信息
                threading.Thread(target=self.fetch_account_details, daemon=True).start()
            else:
                messagebox.showwarning("警告", "文件中没有找到有效的账号信息")

        except Exception as e:
            messagebox.showerror("错误", f"读取文件失败: {e}")
            self.log_message(f"❌ 读取文件失败: {e}")

    def import_from_clipboard(self):
        """从剪贴板导入账号"""
        try:
            clipboard_content = self.root.clipboard_get()
            if clipboard_content:
                # 创建临时文件
                import tempfile
                with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False, encoding='utf-8') as f:
                    f.write(clipboard_content)
                    temp_file = f.name

                # 从临时文件加载
                self.load_accounts_from_file(temp_file)

                # 删除临时文件
                os.unlink(temp_file)

                self.log_message("� 已从剪贴板导入内容")
            else:
                messagebox.showinfo("提示", "剪贴板为空")
        except Exception as e:
            messagebox.showerror("错误", f"从剪贴板导入失败: {e}")

    def clear_accounts(self):
        """清空所有账号"""
        if self.accounts:
            result = messagebox.askyesno("确认", "确定要清空所有账号吗？")
            if result:
                self.accounts.clear()
                for item in self.tree.get_children():
                    self.tree.delete(item)
                self.log_message("🗑️ 已清空所有账号")

    def update_account_table(self):
        """更新账号表格显示"""
        # 清空现有表格内容
        for item in self.tree.get_children():
            self.tree.delete(item)

        # 重新插入所有账号数据
        for i, account in enumerate(self.accounts):
            self.tree.insert("", "end", values=(
                i + 1,  # 序号
                account.uid,  # UID
                account.nickname or "获取中...",  # 昵称
                account.initial_followers,  # 初始粉丝数
                account.current_followers,  # 现有粉丝数
                account.growth,  # 增长数量
                account.status  # 状态
            ))
    
    def fetch_account_details(self):
        """获取账号详细信息（AID、S值、昵称、粉丝数）"""
        self.log_message("🔍 开始获取账号详细信息...")
        
        for i, account in enumerate(self.accounts):
            try:
                self.log_message(f"📋 处理账号 {i+1}/{len(self.accounts)}: {account.uid}")
                
                # 更新状态
                self.update_account_status(i, "获取AID中...")
                
                # 获取AID
                if not self.get_account_aid(account):
                    self.update_account_status(i, "AID获取失败")
                    continue
                
                # 计算S值
                self.update_account_status(i, "计算S值中...")
                if not self.calculate_account_s_value(account):
                    self.update_account_status(i, "S值计算失败")
                    continue
                
                # 获取用户信息和粉丝数
                self.update_account_status(i, "获取用户信息中...")
                if not self.get_account_info(account):
                    # 如果已经标记为异常账号，跳过后续步骤
                    if account.status == "异常账号":
                        self.update_account_status(i, "异常账号")
                        continue
                    else:
                        self.update_account_status(i, "用户信息获取失败")
                        continue

                # 获取初始粉丝数
                self.update_account_status(i, "获取粉丝列表中...")
                if not self.get_initial_followers(account):
                    self.update_account_status(i, "粉丝列表获取失败")
                    continue

                self.update_account_status(i, "就绪")
                self.log_message(f"✅ 账号 {account.uid} ({account.nickname}) 信息获取完成")
                
                # 间隔1秒避免请求过快
                time.sleep(1)
                
            except Exception as e:
                self.log_message(f"❌ 账号 {account.uid} 处理失败: {e}")
                self.update_account_status(i, f"错误: {str(e)[:20]}")
        
        self.log_message("🎉 所有账号信息获取完成！")
    
    def update_account_status(self, index: int, status: str):
        """更新账号状态"""
        if index < len(self.accounts):
            self.accounts[index].status = status
            
            # 更新表格
            items = self.tree.get_children()
            if index < len(items):
                account = self.accounts[index]
                self.tree.item(items[index], values=(
                    index + 1,
                    account.uid,
                    account.nickname or "获取中...",
                    account.initial_followers,
                    account.current_followers,
                    account.get_growth(),
                    account.status
                ))
    
    def get_account_aid(self, account: AccountInfo) -> bool:
        """获取账号AID"""
        try:
            # 第一步：获取登录URL
            url1 = "http://43.241.51.137:5454/asd/dfgh/?uid=aid"
            response1 = requests.get(url1, timeout=10)
            
            if response1.status_code != 200:
                return False
            
            data1 = response1.json()
            if data1.get('code') != 200 or 'result' not in data1 or 'url' not in data1['result']:
                return False
            
            # 第二步：访问登录URL获取AID
            login_url = data1['result']['url']
            response2 = requests.get(login_url, timeout=10)
            
            if response2.status_code != 200:
                return False
            
            data2 = response2.json()
            if 'aid' not in data2:
                return False
            
            account.aid = data2['aid']
            return True
            
        except Exception:
            return False

    def calculate_account_s_value(self, account: AccountInfo) -> bool:
        """计算账号S值"""
        try:
            url = "https://api.weibo.cn/2/users/show"
            headers = {
                "User-Agent": "WeiboOverseas/6.7.9 (iPhone; iOS 18.6.1; Scale/2.00)",
                "Referer": "https://api.weibo.cn/2/users/show"
            }

            # 尝试不同的S值
            s_values = [c*8 for c in "abcdef0123456789"]

            for s in s_values:
                params = {
                    "uid": account.uid,
                    "gsid": account.gsid,
                    "s": s,
                    "c": "weicoabroad",
                    "from": "12DC193010",
                    "ua": "iPhone13,4_iOS18.5_Weibo_intl._6790_wifi__iphone__os18.5",
                    "v_p": "59",
                    "lang": "zh_CN",
                    "aid": account.aid
                }

                try:
                    resp = requests.get(url, headers=headers, params=params, timeout=8)
                    text = resp.text

                    # 只要不是客户端身份校验失败，就判定为S值正确
                    if resp.status_code == 200 and ('客户端身份校验失败' not in text and '"errno":-105' not in text):
                        account.s_value = s
                        return True

                except Exception:
                    continue

            return False

        except Exception:
            return False

    def get_account_info(self, account: AccountInfo) -> bool:
        """获取账号基本信息"""
        try:
            url = "https://api.weibo.cn/2/users/show"
            headers = {
                "User-Agent": "WeiboOverseas/6.7.9 (iPhone; iOS 18.6.1; Scale/2.00)",
                "Cookie": f"SUB={account.gsid}"
            }

            params = {
                "uid": account.uid,
                "gsid": account.gsid,
                "s": account.s_value,
                "c": "weicoabroad",
                "from": "12DC193010",
                "ua": "iPhone13,4_iOS18.5_Weibo_intl._6790_wifi__iphone__os18.5",
                "v_p": "59",
                "lang": "zh_CN",
                "aid": account.aid
            }

            # 设置代理
            proxies = None
            if self.use_proxy and self.current_proxy:
                self.auto_refresh_proxy()
                proxies = self.get_proxy_dict()

            response = requests.get(url, headers=headers, params=params, timeout=10, proxies=proxies)

            if response.status_code == 200:
                try:
                    data = response.json()

                    if 'screen_name' in data:
                        account.nickname = data['screen_name']
                        account.current_followers = data.get('followers_count', 0)
                        self.log_message(f"✅ 用户信息获取成功: {account.nickname}, 粉丝数: {account.current_followers}")
                        return True
                    elif 'errmsg' in data:
                        error_msg = data.get('errmsg', '未知错误')
                        self.log_message(f"❌ 账号异常: {error_msg}")
                        # 标记为异常账号
                        account.status = "异常账号"
                        account.nickname = "异常账号"
                        return False
                    else:
                        self.log_message(f"❌ 用户信息响应格式异常")
                        return False

                except json.JSONDecodeError:
                    self.log_message(f"❌ 用户信息解析失败")
                    return False
            else:
                self.log_message(f"❌ 用户信息请求失败: HTTP {response.status_code}")
                return False

        except Exception as e:
            # 如果是代理相关错误，尝试更换代理
            if self.use_proxy and self.current_proxy and ("proxy" in str(e).lower() or "connection" in str(e).lower()):
                self.log_message(f"🔄 代理连接失败，尝试获取新代理: {e}")
                if self.fetch_new_proxy():
                    # 递归重试一次
                    return self.get_account_info(account)

            self.log_message(f"❌ 获取用户信息异常: {e}")
            return False

    def get_initial_followers(self, account: AccountInfo) -> bool:
        """获取初始粉丝数"""
        try:
            # 获取粉丝列表来确定准确的粉丝数
            followers = self.get_account_followers(account)
            if followers is not None:
                account.initial_followers = len(followers)
                account.current_followers = account.initial_followers
                account.cached_followers = set(followers)
                return True

            return False

        except Exception:
            return False

    def get_account_followers(self, account: AccountInfo) -> Optional[List[str]]:
        """获取账号粉丝列表"""
        try:
            all_followers = []
            page = 1
            max_pages = 5  # 限制最大5页，避免太慢

            while page <= max_pages:
                url = "https://api.weibo.cn/2/friendships/followers"
                headers = {
                    "User-Agent": "WeiboOverseas/6.7.9 (iPhone; iOS 18.6.1; Scale/2.00)",
                    "Cookie": f"SUB={account.gsid}"
                }

                params = {
                    'uid': account.uid,
                    'count': 20,
                    'page': page,
                    'gsid': account.gsid,
                    'aid': account.aid,
                    's': account.s_value,
                    'c': 'weicoabroad',
                    'lang': 'zh_CN',
                    'from': '12DC193010',
                    'trim_status': 1,
                    'ua': 'iPhone13,4_iOS18.5_Weibo_intl._6790_wifi__iphone__os18.5',
                    'v_p': 59
                }

                # 设置代理
                proxies = None
                if self.use_proxy and self.current_proxy:
                    self.auto_refresh_proxy()
                    proxies = self.get_proxy_dict()

                response = requests.get(url, headers=headers, params=params, timeout=10, proxies=proxies)

                if response.status_code == 200:
                    try:
                        data = response.json()

                        if 'users' in data and isinstance(data['users'], list):
                            users = data['users']

                            if len(users) == 0:
                                break

                            # 提取用户UID
                            for user in users:
                                uid = str(user.get('idstr', user.get('id', '')))
                                if uid:
                                    all_followers.append(uid)

                            # 如果这页用户数少于20，认为是最后一页
                            if len(users) < 20:
                                break

                            page += 1
                            time.sleep(0.5)  # 短暂间隔

                        elif 'errmsg' in data:
                            self.log_message(f"❌ 粉丝列表获取失败: {data.get('errmsg', '未知错误')}")
                            return None
                        else:
                            self.log_message(f"❌ 粉丝列表响应格式异常")
                            return None

                    except json.JSONDecodeError:
                        self.log_message(f"❌ 粉丝列表解析失败")
                        return None
                else:
                    self.log_message(f"❌ 粉丝列表请求失败: HTTP {response.status_code}")
                    return None
            return all_followers

        except Exception as e:
            # 如果是代理相关错误，尝试更换代理
            if self.use_proxy and self.current_proxy and ("proxy" in str(e).lower() or "connection" in str(e).lower()):
                self.log_message(f"🔄 代理连接失败，尝试获取新代理: {e}")
                if self.fetch_new_proxy():
                    # 递归重试一次
                    return self.get_account_followers(account)

            self.log_message(f"❌ 获取粉丝列表异常: {e}")
            return None

    def start_monitoring(self):
        """开始监控"""
        if not self.accounts:
            messagebox.showerror("错误", "请先解析账号信息")
            return

        # 检查是否有就绪的账号
        ready_accounts = [acc for acc in self.accounts if acc.status == "就绪"]
        if not ready_accounts:
            messagebox.showerror("错误", "没有就绪的账号可以监控")
            return

        try:
            self.check_interval = int(self.interval_var.get())
            if self.check_interval < 30:
                messagebox.showerror("错误", "检查间隔不能少于30秒")
                return
        except ValueError:
            messagebox.showerror("错误", "检查间隔必须是数字")
            return

        # 不再需要设置单一欢迎文字，现在使用欢迎语列表

        self.is_monitoring = True
        self.start_button.config(state='disabled')
        self.stop_button.config(state='normal')
        self.status_var.set("状态: 监控中...")

        self.log_message(f"🚀 开始监控 {len(ready_accounts)} 个账号")

        # 启动消息发送线程
        if not self.message_thread or not self.message_thread.is_alive():
            self.message_thread = threading.Thread(target=self.message_sender_loop, daemon=True)
            self.message_thread.start()
            self.log_message("📨 消息发送线程已启动")

        # 启动监控线程
        self.monitor_thread = threading.Thread(target=self.monitoring_loop, daemon=True)
        self.monitor_thread.start()

    def stop_monitoring(self):
        """停止监控"""
        self.is_monitoring = False
        self.start_button.config(state='normal')
        self.stop_button.config(state='disabled')
        self.resend_button.config(state='normal')
        self.status_var.set("状态: 已停止")
        self.log_message("🛑 监控已停止")

    def start_resend_messages(self):
        """开始重发私信给前N名粉丝"""
        try:
            count = int(self.resend_count_var.get())
            if count <= 0:
                messagebox.showerror("错误", "请输入有效的粉丝数量")
                return
        except ValueError:
            messagebox.showerror("错误", "请输入有效的数字")
            return

        if not self.welcome_text.strip():
            messagebox.showerror("错误", "请先设置欢迎文字")
            return

        # 检查是否有就绪的账号
        ready_accounts = [acc for acc in self.accounts if acc.status == "就绪"]
        if not ready_accounts:
            messagebox.showerror("错误", "没有就绪的账号可以发送私信")
            return

        # 确认操作
        result = messagebox.askyesno(
            "确认重发",
            f"确定要给所有账号的前{count}名粉丝重新发送私信吗？\n"
            f"将影响 {len(ready_accounts)} 个账号，最多发送 {len(ready_accounts) * count} 条私信。"
        )

        if result:
            # 禁用按钮
            self.resend_button.config(state='disabled')
            self.start_button.config(state='disabled')

            # 在新线程中执行重发任务
            threading.Thread(target=self._resend_messages_thread, args=(count,), daemon=True).start()

    def _resend_messages_thread(self, count: int):
        """重发私信的线程函数"""
        try:
            self.status_var.set("状态: 重发私信中...")
            self.log_message(f"🔄 开始给前{count}名粉丝重发私信...")

            ready_accounts = [acc for acc in self.accounts if acc.status == "就绪"]
            total_sent = 0

            for account in ready_accounts:
                self.log_message(f"📤 处理账号 {account.uid} ({account.nickname})")

                # 获取该账号的粉丝列表
                followers = self.get_account_followers(account)
                if not followers:
                    self.log_message(f"❌ 账号 {account.uid} 粉丝列表获取失败，跳过")
                    continue

                # 取前N名粉丝
                target_followers = followers[:count]
                self.log_message(f"📊 账号 {account.uid} 将给前{len(target_followers)}名粉丝发送私信")

                # 将粉丝加入重发队列
                for i, follower_uid in enumerate(target_followers, 1):
                    self.message_queue.put({
                        'type': 'resend',
                        'account': account,
                        'follower_uid': follower_uid,
                        'timestamp': time.time()
                    })
                    self.log_message(f"📝 [{i}/{len(target_followers)}] 粉丝 {follower_uid} 已加入重发队列")

                total_sent += len(target_followers)

                # 账号间隔
                time.sleep(0.1)

            # 完成统计
            self.log_message(f"🎉 重发私信任务已加入队列！共 {total_sent} 条消息")
            self.status_var.set(f"状态: 重发任务已提交 (共{total_sent}条)")

        except Exception as e:
            self.log_message(f"❌ 重发私信异常: {e}")
            self.status_var.set("状态: 重发失败")
        finally:
            # 恢复按钮状态
            self.root.after(0, lambda: self.resend_button.config(state='normal'))
            self.root.after(0, lambda: self.start_button.config(state='normal'))

    def monitoring_loop(self):
        """监控循环"""
        while self.is_monitoring:
            try:
                ready_accounts = [acc for acc in self.accounts if acc.status == "就绪"]

                for account in ready_accounts:
                    if not self.is_monitoring:
                        break

                    self.log_message(f"🔍 检查账号 {account.uid} ({account.nickname})")

                    # 获取当前粉丝列表
                    current_followers = self.get_account_followers(account)

                    if current_followers is not None:
                        current_set = set(current_followers)
                        old_count = len(account.cached_followers)
                        new_count = len(current_followers)

                        self.log_message(f"📊 账号 {account.uid} 粉丝变化: {old_count} → {new_count}")

                        # 检测新增粉丝
                        new_followers = current_set - account.cached_followers
                        lost_followers = account.cached_followers - current_set

                        if new_followers:
                            self.log_message(f"🎉 账号 {account.uid} 发现 {len(new_followers)} 个新粉丝: {list(new_followers)[:5]}...")

                            # 将新粉丝加入消息发送队列
                            for follower_uid in new_followers:
                                self.message_queue.put({
                                    'type': 'welcome',
                                    'account': account,
                                    'follower_uid': follower_uid,
                                    'timestamp': time.time()
                                })
                                self.log_message(f"📝 新粉丝 {follower_uid} 已加入发送队列")

                        if lost_followers:
                            self.log_message(f"📉 账号 {account.uid} 失去 {len(lost_followers)} 个粉丝")

                        if not new_followers and not lost_followers:
                            self.log_message(f"📝 账号 {account.uid} 粉丝无变化")

                        # 更新数据
                        account.current_followers = len(current_followers)
                        account.cached_followers = current_set

                        # 更新表格显示
                        account_index = self.accounts.index(account)
                        self.root.after(0, lambda idx=account_index: self.update_account_display(idx))
                    else:
                        self.log_message(f"❌ 账号 {account.uid} 粉丝列表获取失败")

                    # 间隔避免请求过快 (100毫秒)
                    time.sleep(0.1)

                # 等待下次检查
                for _ in range(self.check_interval):
                    if not self.is_monitoring:
                        break
                    time.sleep(1)

            except Exception as e:
                self.log_message(f"❌ 监控过程出错: {e}")
                time.sleep(10)

    def message_sender_loop(self):
        """消息发送线程循环"""
        self.log_message("📨 消息发送线程开始运行")

        while self.is_monitoring:
            try:
                # 从队列中获取消息任务，超时1秒
                try:
                    message_task = self.message_queue.get(timeout=1)
                except:
                    continue  # 超时继续循环

                if message_task['type'] == 'welcome':
                    # 发送欢迎消息
                    account = message_task['account']
                    follower_uid = message_task['follower_uid']

                    self.log_message(f"📤 开始向粉丝 {follower_uid} 发送欢迎消息")
                    self.send_welcome_message_sync(account, follower_uid)

                elif message_task['type'] == 'resend':
                    # 重发消息
                    account = message_task['account']
                    follower_uid = message_task['follower_uid']

                    self.log_message(f"🔄 开始重发消息给粉丝 {follower_uid}")
                    self.send_welcome_message_sync(account, follower_uid)

                # 标记任务完成
                self.message_queue.task_done()

            except Exception as e:
                self.log_message(f"❌ 消息发送线程异常: {e}")
                time.sleep(1)

        self.log_message("📨 消息发送线程已停止")

    def load_config(self):
        """加载配置文件"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)

                # 加载代理设置
                if 'proxy' in config:
                    proxy_config = config['proxy']
                    self.use_proxy = proxy_config.get('enabled', False)
                    self.proxy_api_url = proxy_config.get('api_url', '')
                    self.proxy_refresh_interval = proxy_config.get('refresh_interval', 1)

                # 加载欢迎语设置
                if 'welcome_messages' in config:
                    self.welcome_messages = config['welcome_messages']
                    # 更新下一个ID
                    if self.welcome_messages:
                        max_id = max(msg.get('id', 0) for msg in self.welcome_messages)
                        self.next_welcome_id = max_id + 1

                if hasattr(self, 'log_text'):
                    self.log_message("✅ 配置文件加载成功")
            else:
                if hasattr(self, 'log_text'):
                    self.log_message("📝 使用默认配置")
        except Exception as e:
            if hasattr(self, 'log_text'):
                self.log_message(f"❌ 配置文件加载失败: {e}")

    def save_config(self):
        """保存配置文件"""
        try:
            config = {
                'proxy': {
                    'enabled': self.use_proxy,
                    'api_url': self.proxy_api_url,
                    'refresh_interval': self.proxy_refresh_interval
                },
                'welcome_messages': self.welcome_messages
            }

            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)

            self.log_message("✅ 配置已自动保存")
        except Exception as e:
            self.log_message(f"❌ 配置保存失败: {e}")

    def update_account_display(self, index: int):
        """更新账号显示"""
        if index < len(self.accounts):
            account = self.accounts[index]
            items = self.tree.get_children()
            if index < len(items):
                self.tree.item(items[index], values=(
                    index + 1,
                    account.uid,
                    account.nickname,
                    account.initial_followers,
                    account.current_followers,
                    account.get_growth(),
                    account.status
                ))

    def send_welcome_message_sync(self, account: AccountInfo, follower_uid: str):
        """发送欢迎消息"""
        try:
            if not self.welcome_messages:
                self.log_message(f"⚠️ 没有配置欢迎语，跳过向粉丝 {follower_uid} 发送消息")
                return

            success_count = 0
            total_count = len(self.welcome_messages)

            # 发送所有配置的欢迎语
            for i, msg in enumerate(self.welcome_messages):
                try:
                    if msg['type'] == 'text':
                        # 发送文字消息
                        if self.send_text_message(account, follower_uid, msg['content']):
                            success_count += 1
                            self.log_message(f"✅ 向粉丝 {follower_uid} 发送文字欢迎语 {i+1} 成功")
                        else:
                            self.log_message(f"❌ 向粉丝 {follower_uid} 发送文字欢迎语 {i+1} 失败")

                    elif msg['type'] == 'image':
                        # 发送图片消息
                        if self.send_image_message(account, follower_uid, msg['content']):
                            success_count += 1
                            self.log_message(f"✅ 向粉丝 {follower_uid} 发送图片欢迎语 {i+1} 成功")
                        else:
                            self.log_message(f"❌ 向粉丝 {follower_uid} 发送图片欢迎语 {i+1} 失败")

                    # 消息间隔，避免发送太快
                    if i < len(self.welcome_messages) - 1:
                        time.sleep(1)

                except Exception as e:
                    self.log_message(f"❌ 发送欢迎语 {i+1} 异常: {e}")

            # 总结发送结果
            if success_count == total_count:
                self.log_message(f"🎉 向粉丝 {follower_uid} 发送所有 {total_count} 条欢迎语成功")
            elif success_count > 0:
                self.log_message(f"⚠️ 向粉丝 {follower_uid} 发送欢迎语部分成功 ({success_count}/{total_count})")
            else:
                self.log_message(f"❌ 向粉丝 {follower_uid} 发送所有欢迎语失败")

        except Exception as e:
            self.log_message(f"❌ 发送欢迎消息异常: {e}")

    def send_text_message(self, account: AccountInfo, to_uid: str, text: str) -> bool:
        """发送文字私信"""
        try:
            # 自动刷新代理
            if self.use_proxy:
                self.auto_refresh_proxy()

            # 创建会话并设置Cookie和代理
            session = requests.Session()
            session.cookies.update({'SUB': account.gsid})

            # 设置代理
            if self.use_proxy and self.current_proxy:
                proxy_dict = self.get_proxy_dict()
                session.proxies.update(proxy_dict)

            url = "https://api.weibo.com/webim/2/direct_messages/new.json"
            headers = {
                "Content-Type": "application/x-www-form-urlencoded; charset=utf-8",
                "Referer": "https://api.weibo.com/chat/"
            }

            data = {
                'decodetime': '1',
                'is_encoded': '0',
                'media_type': '0',
                'source': '*********',
                'text': text,
                'uid': to_uid
            }

            response = session.post(url, headers=headers, data=data, timeout=10)
            response.raise_for_status()

            result = response.json()
            if 'error' in result or 'errmsg' in result:
                error_msg = result.get('error', result.get('errmsg', '未知错误'))
                self.log_message(f"❌ 文字消息发送失败: {error_msg}")
                return False
            else:
                return True

        except requests.exceptions.RequestException as e:
            # 如果是代理相关错误，尝试更换代理并重试
            if self.use_proxy and self.current_proxy and ("proxy" in str(e).lower() or "connection" in str(e).lower()):
                self.log_message(f"🔄 代理连接失败，尝试获取新代理: {e}")
                if self.fetch_new_proxy():
                    # 递归重试一次
                    return self.send_text_message(account, to_uid, text)

            self.log_message(f"❌ 文字消息请求失败: {e}")
            return False
        except Exception as e:
            self.log_message(f"❌ 发送文字消息异常: {e}")
            return False

    def send_image_message(self, account: AccountInfo, to_uid: str, image_path: str) -> bool:
        """发送图片私信"""
        try:
            if not os.path.exists(image_path):
                self.log_message(f"❌ 图片文件不存在: {image_path}")
                return False

            # 自动刷新代理
            if self.use_proxy:
                self.auto_refresh_proxy()

            # 创建会话并设置Cookie和代理
            session = requests.Session()
            session.cookies.update({'SUB': account.gsid})

            # 设置代理
            if self.use_proxy and self.current_proxy:
                proxy_dict = self.get_proxy_dict()
                session.proxies.update(proxy_dict)

            # 1. 初始化上传
            init_result = self._init_upload(session, account, image_path, to_uid)
            if not init_result:
                return False

            file_token = init_result.get('fileToken')
            url_tag = init_result.get('urlTag')

            if not file_token or not url_tag:
                self.log_message("❌ 获取上传令牌失败")
                return False

            # 2. 上传文件
            fid = self._upload_file(session, account, image_path, file_token, str(url_tag))
            if not fid:
                return False

            # 3. 发送图片消息
            url = "https://api.weibo.com/webim/2/direct_messages/new.json"
            headers = {
                "Content-Type": "application/x-www-form-urlencoded; charset=utf-8",
                "Referer": "https://api.weibo.com/chat/"
            }

            data = {
                'decodetime': '1',
                'fids': fid,
                'is_encoded': '0',
                'media_type': '1',  # 1表示图片消息
                'source': '*********',
                'text': '分享图片',
                'uid': to_uid
            }

            response = session.post(url, headers=headers, data=data, timeout=10)
            response.raise_for_status()

            result = response.json()
            if 'error' in result or 'errmsg' in result:
                error_msg = result.get('error', result.get('errmsg', '未知错误'))
                self.log_message(f"❌ 图片消息发送失败: {error_msg}")
                return False
            else:
                return True

        except requests.exceptions.RequestException as e:
            self.log_message(f"❌ 图片消息请求失败: {e}")
            return False
        except Exception as e:
            self.log_message(f"❌ 发送图片消息异常: {e}")
            return False

    def _calculate_md5(self, file_path: str) -> str:
        """计算文件MD5"""
        hash_md5 = hashlib.md5()
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_md5.update(chunk)
        return hash_md5.hexdigest()

    def _init_upload(self, session: requests.Session, account: AccountInfo, file_path: str, recipient_uid: str) -> Optional[Dict[str, Any]]:
        """初始化文件上传"""
        try:
            file_size = os.path.getsize(file_path)
            file_md5 = self._calculate_md5(file_path)
            file_name = os.path.basename(file_path)

            url = "https://upload.api.weibo.com/fileplatform/init.json"

            # 准备参数
            extprops = json.dumps({
                "uploadType": 1,
                "recipientId": int(recipient_uid)
            })

            mediaprops = json.dumps({
                "ori": 0,
                "ignore_rotate": 0,
                "createtype": "localfile",
                "print_mark": 0
            })

            params = {
                'act': 'init',
                'aid': account.aid,
                'c': 'weicoabroad',
                'check': file_md5,
                'extprops': extprops,
                'file_source': '9',
                'from': '**********',
                'gsid': account.gsid,
                'lang': 'zh_CN',
                'length': str(file_size),
                'md5': file_md5,
                'mediaprops': mediaprops,
                'name': file_name,
                's': account.s_value,
                'source': '**********',
                'status': 'wifi',
                'type': 'dm_attachment_pic'
            }

            response = session.get(url, params=params, timeout=10)
            response.raise_for_status()

            result = response.json()
            return result

        except Exception as e:
            self.log_message(f"❌ 文件上传初始化失败: {e}")
            return None

    def _upload_file(self, session: requests.Session, account: AccountInfo, file_path: str, file_token: str, url_tag: str) -> Optional[str]:
        """上传文件数据"""
        try:
            file_size = os.path.getsize(file_path)
            file_md5 = self._calculate_md5(file_path)

            url = "https://upload.api.weibo.com/fileplatform/upload.json"

            # 准备参数
            params = {
                'sectioncheck': file_md5,
                'chunkindex': '0',
                'source': '**********',
                'c': 'weicoabroad',
                'lang': 'zh_CN',
                'chunkcount': '1',
                's': account.s_value,
                'filetoken': file_token,
                'aid': account.aid,
                'urltag': url_tag,
                'file_source': '9',
                'type': 'dm_attachment_pic',
                'ft': '0',
                'gsid': account.gsid,
                'startloc': '0',
                'from': '**********',
                'filecheck': file_md5,
                'chunksize': str(file_size),
                'networktype': 'wifi',
                'filelength': str(file_size)
            }

            # 设置请求头
            headers = {
                'Content-Type': 'application/octet-stream; charset=utf-8',
                'Upload-Draft-Interop-Version': '6',
                'Upload-Complete': '?1'
            }

            # 读取文件数据
            with open(file_path, 'rb') as f:
                file_data = f.read()

            response = session.post(url, params=params, headers=headers, data=file_data, timeout=30)
            response.raise_for_status()

            result = response.json()
            if 'fid' in result:
                return result['fid']
            else:
                self.log_message(f"❌ 文件上传失败: {result}")
                return None

        except Exception as e:
            self.log_message(f"❌ 文件上传异常: {e}")
            return None

    def refresh_status(self):
        """刷新状态"""
        self.log_message("🔄 刷新账号状态...")

        for i, account in enumerate(self.accounts):
            if account.status == "就绪":
                # 重新获取粉丝数
                current_followers = self.get_account_followers(account)
                if current_followers is not None:
                    account.current_followers = len(current_followers)
                    self.update_account_display(i)

        self.log_message("✅ 状态刷新完成")


def main():
    root = tk.Tk()
    app = WeiboBatchMonitorGUI(root)
    root.mainloop()


if __name__ == '__main__':
    main()
