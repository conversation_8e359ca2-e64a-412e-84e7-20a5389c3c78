#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
微博国际版私信发送工具
支持发送文字和图片
"""

import requests
import hashlib
import json
import os
import urllib.parse
from typing import Optional, Dict, Any


class WeiboSender:
    def __init__(self, uid: str, gsid: str, s_value: str, aid: str, user_agent: str = None):
        """
        初始化微博发送器
        
        Args:
            uid: 你的UID
            gsid: GSID值
            s_value: S值
            aid: AID设备标识
            user_agent: 用户代理字符串，默认使用iOS版本
        """
        self.uid = uid
        self.gsid = gsid
        self.s_value = s_value
        self.aid = aid
        
        # 默认User-Agent
        self.user_agent = user_agent or "WeiboOverseas/6.7.9 (com.weibo.international; build:*******; iOS 18.5.0) Alamofire/5.10.2"
        
        # 会话对象
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': self.user_agent,
            'Accept': '*/*',
            'Accept-Language': 'zh-Hans-GB;q=1, zh-Hant-GB;q=0.9, yue-Hant-GB;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br'
        })
    
    def set_cookies(self, sub: str, subp: str = None):
        """
        设置登录Cookie

        Args:
            sub: SUB cookie值
            subp: SUBP cookie值（可选，根据测试可能不是必需的）
        """
        cookies = {'SUB': sub}
        if subp:
            cookies['SUBP'] = subp
        self.session.cookies.update(cookies)
    
    def send_text(self, recipient_uid: str, text: str) -> Dict[str, Any]:
        """
        发送文字消息
        
        Args:
            recipient_uid: 接收者UID
            text: 要发送的文字内容
            
        Returns:
            API响应结果
        """
        url = "https://api.weibo.com/webim/2/direct_messages/new.json"
        
        # 准备POST数据
        data = {
            'decodetime': '1',
            'is_encoded': '0',
            'media_type': '0',
            'source': '209678993',
            'text': text,
            'uid': recipient_uid
        }
        
        # 设置请求头
        headers = {
            'Content-Type': 'application/x-www-form-urlencoded; charset=utf-8',
            'Referer': 'https://api.weibo.com/chat/'
        }
        
        try:
            response = self.session.post(url, data=data, headers=headers)
            response.raise_for_status()
            
            result = response.json()
            print(f"文字消息发送成功: {result.get('text', '')}")
            return result
            
        except requests.exceptions.RequestException as e:
            print(f"发送文字消息失败: {e}")
            return {'error': str(e)}
        except json.JSONDecodeError as e:
            print(f"解析响应失败: {e}")
            return {'error': f'JSON解析错误: {e}'}
    
    def _calculate_md5(self, file_path: str) -> str:
        """计算文件MD5值"""
        hash_md5 = hashlib.md5()
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_md5.update(chunk)
        return hash_md5.hexdigest()
    
    def _init_upload(self, file_path: str, recipient_uid: str) -> Optional[Dict[str, Any]]:
        """
        初始化文件上传
        
        Args:
            file_path: 文件路径
            recipient_uid: 接收者UID
            
        Returns:
            初始化响应结果
        """
        file_size = os.path.getsize(file_path)
        file_md5 = self._calculate_md5(file_path)
        file_name = os.path.basename(file_path)
        
        url = "https://upload.api.weibo.com/fileplatform/init.json"
        
        # 准备参数
        extprops = json.dumps({
            "uploadType": 1,
            "recipientId": int(recipient_uid)
        })
        
        mediaprops = json.dumps({
            "ori": 0,
            "ignore_rotate": 0,
            "createtype": "localfile",
            "print_mark": 0
        })
        
        params = {
            'act': 'init',
            'aid': self.aid,
            'c': 'weicoabroad',
            'check': file_md5,
            'extprops': extprops,
            'file_source': '9',
            'from': '1267993010',
            'gsid': self.gsid,
            'lang': 'zh_CN',
            'length': str(file_size),
            'md5': file_md5,
            'mediaprops': mediaprops,
            'name': file_name,
            's': self.s_value,
            'source': '4215535043',
            'status': 'wifi',
            'type': 'dm_attachment_pic'
        }
        
        try:
            response = self.session.get(url, params=params)
            response.raise_for_status()
            
            result = response.json()
            print(f"文件上传初始化成功: {result}")
            return result
            
        except requests.exceptions.RequestException as e:
            print(f"文件上传初始化失败: {e}")
            return None
        except json.JSONDecodeError as e:
            print(f"解析初始化响应失败: {e}")
            return None
    
    def _upload_file(self, file_path: str, file_token: str, url_tag: str) -> Optional[str]:
        """
        上传文件数据

        Args:
            file_path: 文件路径
            file_token: 文件令牌
            url_tag: URL标签

        Returns:
            上传成功返回fid，失败返回None
        """
        file_size = os.path.getsize(file_path)
        file_md5 = self._calculate_md5(file_path)
        
        url = "https://upload.api.weibo.com/fileplatform/upload.json"
        
        # 准备参数
        params = {
            'sectioncheck': file_md5,
            'chunkindex': '0',
            'source': '4215535043',
            'c': 'weicoabroad',
            'lang': 'zh_CN',
            'chunkcount': '1',
            's': self.s_value,
            'filetoken': file_token,
            'aid': self.aid,
            'urltag': url_tag,
            'file_source': '9',
            'type': 'dm_attachment_pic',
            'ft': '0',
            'gsid': self.gsid,
            'startloc': '0',
            'from': '1267993010',
            'filecheck': file_md5,
            'chunksize': str(file_size),
            'networktype': 'wifi',
            'filelength': str(file_size)
        }
        
        # 设置请求头
        headers = {
            'Content-Type': 'application/octet-stream; charset=utf-8',
            'Upload-Draft-Interop-Version': '6',
            'Upload-Complete': '?1'
        }
        
        try:
            # 读取文件数据
            with open(file_path, 'rb') as f:
                file_data = f.read()
            
            response = self.session.post(url, params=params, data=file_data, headers=headers)
            response.raise_for_status()

            # 解析响应获取fid
            result = response.json()
            fid = result.get('fid')
            if fid:
                print(f"文件上传成功，获得fid: {fid}")
                return fid
            else:
                print(f"文件上传失败：未获得fid")
                return None

        except requests.exceptions.RequestException as e:
            print(f"文件上传失败: {e}")
            return None
        except json.JSONDecodeError as e:
            print(f"解析上传响应失败: {e}")
            return None
    
    def send_image(self, recipient_uid: str, image_path: str) -> Dict[str, Any]:
        """
        发送图片消息

        Args:
            recipient_uid: 接收者UID
            image_path: 图片文件路径

        Returns:
            发送结果
        """
        if not os.path.exists(image_path):
            return {'error': f'文件不存在: {image_path}'}

        # 1. 初始化上传
        init_result = self._init_upload(image_path, recipient_uid)
        if not init_result:
            return {'error': '文件上传初始化失败'}

        file_token = init_result.get('fileToken')
        url_tag = init_result.get('urlTag')

        if not file_token or not url_tag:
            return {'error': '获取上传令牌失败'}

        # 2. 上传文件
        fid = self._upload_file(image_path, file_token, str(url_tag))
        if not fid:
            return {'error': '文件上传失败'}

        # 3. 发送图片消息给接收者（使用fid，不是fileToken）
        send_result = self._send_image_message(recipient_uid, fid)
        if not send_result:
            return {'error': '发送图片消息失败'}

        print(f"图片发送成功: {image_path}")
        return {'success': True, 'fileToken': file_token, 'fid': fid, 'message_result': send_result}

    def _send_image_message(self, recipient_uid: str, fid: str) -> Optional[Dict[str, Any]]:
        """
        发送图片消息给接收者

        Args:
            recipient_uid: 接收者UID
            fid: 文件ID（从上传步骤获得）

        Returns:
            发送结果
        """
        url = "https://api.weibo.com/webim/2/direct_messages/new.json"

        # 准备POST数据 - 图片消息的参数
        data = {
            'decodetime': '1',
            'fids': fid,  # 使用fids参数，传入从上传步骤获得的fid
            'is_encoded': '0',
            'media_type': '1',  # 1表示图片消息，0表示文字消息
            'source': '209678993',
            'text': '分享图片',  # 图片消息需要text参数，与抓包数据一致
            'uid': recipient_uid
        }

        # 设置请求头
        headers = {
            'Content-Type': 'application/x-www-form-urlencoded; charset=utf-8',
            'Referer': 'https://api.weibo.com/chat/'
        }

        try:
            response = self.session.post(url, data=data, headers=headers)
            response.raise_for_status()

            result = response.json()
            print(f"图片消息发送API调用成功: {result}")
            return result

        except requests.exceptions.RequestException as e:
            print(f"发送图片消息API调用失败: {e}")
            return None
        except json.JSONDecodeError as e:
            print(f"解析图片消息响应失败: {e}")
            return None


def main():
    """示例使用"""
    # 配置参数 - 请替换为你的实际参数
    config = {
        'uid': 'YOUR_UID',
        'gsid': 'YOUR_GSID',
        's_value': 'YOUR_S_VALUE',
        'aid': 'YOUR_AID',
        'sub': 'YOUR_SUB_COOKIE',
        'subp': 'YOUR_SUBP_COOKIE'  # Optional parameter
    }

    # 创建发送器实例
    sender = WeiboSender(
        uid=config['uid'],
        gsid=config['gsid'],
        s_value=config['s_value'],
        aid=config['aid']
    )

    # 设置登录Cookie（SUBP是可选的）
    sender.set_cookies(config['sub'], config.get('subp'))
    
    # 接收者UID
    recipient_uid = '7993067096'
    
    # 发送文字消息
    text_result = sender.send_text(recipient_uid, 'Hello, this is a test message!')
    print(f"Text send result: {text_result}")

    # 发送图片消息
    image_path = 'test_image.jpg'  # Replace with your image path
    if os.path.exists(image_path):
        image_result = sender.send_image(recipient_uid, image_path)
        print(f"Image send result: {image_result}")
    else:
        print(f"Image file not found: {image_path}")


if __name__ == '__main__':
    main()
