#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
微博粉丝监控测试 - 使用移动端API
按照新的逻辑进行测试
"""

import requests
import json
import time
from typing import Set, List, Dict, Any, Optional


class WeiboFollowersTest:
    def __init__(self, uid: str, gsid: str, s_value: str, aid: str, sub: str, subp: str = None):
        """
        初始化测试类
        
        Args:
            uid: 微博UID
            gsid: GSID值
            s_value: S值
            aid: AID设备标识
            sub: SUB Cookie值
            subp: SUBP Cookie值（可选）
        """
        self.uid = uid
        self.gsid = gsid
        self.s_value = s_value
        self.aid = aid
        self.sub = sub
        self.subp = subp
        
        # 创建会话，模拟iPhone客户端
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'WeiboOverseas/6.7.9 (iPhone; iOS 18.5; Scale/3.00)',
            'Accept': '*/*',
            'Accept-Language': 'zh-Hans-GB;q=1, zh-Hant-GB;q=0.9, yue-Hant-GB;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br'
        })
        
        # 设置Cookie
        cookies = {'SUB': sub}
        if subp:
            cookies['SUBP'] = subp
        self.session.cookies.update(cookies)
        
        # 粉丝缓存
        self.cached_followers: Set[str] = set()
    
    def get_followers_page(self, page: int = 1) -> Optional[List[Dict[str, Any]]]:
        """
        获取指定页的粉丝列表
        
        Args:
            page: 页码，从1开始
            
        Returns:
            粉丝列表，失败返回None
        """
        url = "https://api.weibo.cn/2/friendships/followers"
        
        params = {
            'uid': self.uid,
            'count': 20,  # 每页20个用户
            'page': page,
            'gsid': self.gsid,
            'aid': self.aid,
            's': self.s_value,
            'c': 'weicoabroad',
            'lang': 'zh_CN',
            'from': '12DC193010',
            'trim_status': 1,
            'ua': 'iPhone13,4_iOS18.5_Weibo_intl._6790_wifi__iphone__os18.5',
            'v_p': 59
        }
        
        try:
            print(f"📄 获取第{page}页粉丝...")
            response = self.session.get(url, params=params)
            
            print(f"状态码: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                
                if 'users' in data and isinstance(data['users'], list):
                    users = data['users']
                    print(f"✅ 第{page}页获取成功: {len(users)}个用户")
                    return users
                else:
                    print(f"❌ 响应格式异常: {list(data.keys()) if isinstance(data, dict) else type(data)}")
                    if isinstance(data, dict) and 'errmsg' in data:
                        print(f"错误信息: {data['errmsg']}")
                    return None
            else:
                print(f"❌ HTTP错误: {response.status_code}")
                print(f"响应内容: {response.text[:200]}...")
                return None
                
        except requests.exceptions.RequestException as e:
            print(f"❌ 请求失败: {e}")
            return None
        except json.JSONDecodeError as e:
            print(f"❌ JSON解析失败: {e}")
            return None
    
    def get_all_followers(self) -> Set[str]:
        """
        获取所有粉丝的UID集合
        
        Returns:
            粉丝UID集合
        """
        all_followers = set()
        page = 1
        max_pages = 10  # 限制最大10页
        
        print("🔍 开始获取完整粉丝列表...")
        
        while page <= max_pages:
            users = self.get_followers_page(page)
            
            if users is None:
                print(f"❌ 第{page}页获取失败，停止获取")
                break
            
            if len(users) == 0:
                print(f"📄 第{page}页无数据，已获取完所有粉丝")
                break
            
            # 提取用户UID
            page_followers = set()
            for user in users:
                uid = str(user.get('idstr', user.get('id', '')))
                if uid:
                    page_followers.add(uid)
                    all_followers.add(uid)
            
            print(f"📊 第{page}页粉丝UID: {list(page_followers)[:3]}... (共{len(page_followers)}个)")
            
            # 如果这页用户数少于20，认为是最后一页
            if len(users) < 20:
                print(f"📄 第{page}页用户数({len(users)}) < 20，认为是最后一页")
                break
            
            page += 1
            
            # 页面间隔1秒，避免请求过快
            if page <= max_pages:
                print("⏱️ 等待1秒...")
                time.sleep(1)
        
        print(f"✅ 获取完成，总计 {len(all_followers)} 个粉丝")
        return all_followers
    
    def detect_new_followers(self) -> Set[str]:
        """
        检测新增粉丝
        
        Returns:
            新增粉丝UID集合
        """
        print("\n🔍 检测新增粉丝...")
        
        # 获取当前粉丝列表
        current_followers = self.get_all_followers()
        
        if not current_followers:
            print("❌ 无法获取当前粉丝列表")
            return set()
        
        # 对比缓存，找出新增粉丝
        if self.cached_followers:
            new_followers = current_followers - self.cached_followers
            print(f"📊 对比结果:")
            print(f"  - 当前粉丝数: {len(current_followers)}")
            print(f"  - 缓存粉丝数: {len(self.cached_followers)}")
            print(f"  - 新增粉丝数: {len(new_followers)}")
            
            if new_followers:
                print(f"🎉 发现新增粉丝: {list(new_followers)}")
            else:
                print("📝 暂无新增粉丝")
        else:
            print("📝 首次运行，初始化粉丝缓存")
            new_followers = set()
        
        # 更新缓存
        self.cached_followers = current_followers.copy()
        print("💾 已更新粉丝缓存")
        
        return new_followers
    
    def test_monitoring_cycle(self):
        """测试完整的监控周期"""
        print("🚀 开始测试粉丝监控周期")
        print("=" * 60)
        
        # 第一次检测（初始化）
        print("\n【第一次检测 - 初始化】")
        new_followers_1 = self.detect_new_followers()
        
        print(f"\n⏱️ 等待30秒后进行第二次检测...")
        time.sleep(30)
        
        # 第二次检测（检测新增）
        print("\n【第二次检测 - 检测新增】")
        new_followers_2 = self.detect_new_followers()
        
        print("\n📊 测试结果总结:")
        print(f"  - 第一次检测: 初始化了 {len(self.cached_followers)} 个粉丝")
        print(f"  - 第二次检测: 发现 {len(new_followers_2)} 个新增粉丝")
        
        if new_followers_2:
            print(f"  - 新增粉丝UID: {list(new_followers_2)}")
        
        return len(new_followers_2) > 0


def main():
    """主测试函数"""
    # 使用你的实际参数
    config = {
        'uid': '8006397427',
        'gsid': '_2A25FUEv5DeRxGe5O61QS-SnIyTuIHXVkRNgxrDV6PUJbkdANLW_xkWpNdb9f5qCQjSx0wHS_YSeHTcOahdx04gJE',
        's_value': 'aaaaaaaa',  # 你修改后的值
        'aid': '01A89NOEaS1qb_TLz0zNLg-hL-gQgk3TIMy3MKp9bRI-1bmXg.',
        'sub': '_2A25FUEv5DeRxGe5O61QS-SnIyTuIHXVkRNgxrDV6PUJbkdANLW_xkWpNdb9f5qCQjSx0wHS_YSeHTcOahdx04gJE',
        'subp': None
    }
    
    print("🧪 微博粉丝监控测试")
    print("=" * 60)
    print(f"测试账号UID: {config['uid']}")
    print(f"使用API: https://api.weibo.cn/2/friendships/followers")
    print()
    
    # 创建测试实例
    tester = WeiboFollowersTest(
        uid=config['uid'],
        gsid=config['gsid'],
        s_value=config['s_value'],
        aid=config['aid'],
        sub=config['sub'],
        subp=config.get('subp')
    )
    
    # 选择测试模式
    print("请选择测试模式:")
    print("1. 单次获取粉丝列表测试")
    print("2. 完整监控周期测试")
    
    try:
        choice = input("请输入选择 (1 或 2): ").strip()
        
        if choice == '1':
            print("\n🔍 开始单次获取粉丝列表测试...")
            followers = tester.get_all_followers()
            if followers:
                print(f"✅ 测试成功！获取到 {len(followers)} 个粉丝")
                print(f"前5个粉丝UID: {list(followers)[:5]}")
            else:
                print("❌ 测试失败！无法获取粉丝列表")
                
        elif choice == '2':
            print("\n🔄 开始完整监控周期测试...")
            has_new = tester.test_monitoring_cycle()
            if has_new:
                print("✅ 监控测试成功！检测到新增粉丝")
            else:
                print("✅ 监控测试完成！暂无新增粉丝")
        else:
            print("❌ 无效选择")
            
    except KeyboardInterrupt:
        print("\n\n⏹️ 测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试过程中出错: {e}")


if __name__ == '__main__':
    main()
