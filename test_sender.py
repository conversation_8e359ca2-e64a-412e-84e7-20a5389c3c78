#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
微博发送器测试文件
请在这里配置你的实际参数进行测试
"""

from weibo_sender import WeiboSender
import os

def main():
    """测试发送功能"""
    
    # 请在这里填入你的实际参数
    config = {
        'uid': 'YOUR_UID',                    # 你的微博UID
        'gsid': 'YOUR_GSID',                  # 从抓包获取的GSID
        's_value': 'dddddddd',                # S值，通常是dddddddd
        'aid': 'YOUR_AID',                    # 设备标识符AID
        'sub': 'YOUR_SUB_COOKIE',             # SUB Cookie值
        'subp': None                          # SUBP Cookie值（可选，可以设为None）
    }
    
    # 接收者UID
    recipient_uid = '7993067096'  # 替换为实际的接收者UID
    
    # 检查配置是否已填写
    if config['uid'] == 'YOUR_UID':
        print("请先在config中填入你的实际参数！")
        print("需要填写的参数：")
        print("- uid: 你的微博UID")
        print("- gsid: 从抓包获取的GSID")
        print("- s_value: S值（通常是dddddddd）")
        print("- aid: 设备标识符AID")
        print("- sub: SUB Cookie值")
        print("- subp: SUBP Cookie值（可选）")
        return
    
    # 创建发送器实例
    sender = WeiboSender(
        uid=config['uid'],
        gsid=config['gsid'],
        s_value=config['s_value'],
        aid=config['aid']
    )
    
    # 设置登录Cookie（SUBP是可选的）
    sender.set_cookies(config['sub'], config.get('subp'))
    
    print("开始测试发送功能...")
    
    # 测试发送文字消息
    print("\n1. 测试发送文字消息...")
    text_result = sender.send_text(recipient_uid, '你好，这是一条测试消息！🎉')
    print(f"文字发送结果: {text_result}")
    
    # 测试发送图片消息
    print("\n2. 测试发送图片消息...")
    image_path = 'test.jpg'  # 你可以将测试图片命名为test.jpg放在当前目录
    
    if os.path.exists(image_path):
        image_result = sender.send_image(recipient_uid, image_path)
        print(f"图片发送结果: {image_result}")
    else:
        print(f"测试图片不存在: {image_path}")
        print("请将要测试的图片重命名为 test.jpg 并放在当前目录下")
    
    print("\n测试完成！")

if __name__ == '__main__':
    main()
