# 微博国际版粉丝监控工具使用说明

## 🎯 功能介绍

这个工具可以自动监控你的微博账号新增粉丝，并向新粉丝发送欢迎消息（文字+图片）。

### 主要功能：
- ✅ 自动监控新增粉丝
- ✅ 向新粉丝发送欢迎文字消息
- ✅ 向新粉丝发送欢迎图片消息
- ✅ 可自定义检查间隔
- ✅ 实时日志显示
- ✅ 配置保存/加载

## 🚀 使用方法

### 1. 启动GUI版本
```bash
python weibo_monitor_gui.py
```

### 2. 配置参数

#### 必需参数：
- **你的UID**: 你的微博用户ID
- **GSID**: 从抓包数据中获取
- **S值**: 通常是 `dddddddd`
- **AID**: 设备标识符
- **SUB Cookie**: 登录凭证

#### 可选参数：
- **SUBP Cookie**: 额外的登录凭证（可留空）

### 3. 监控设置

- **检查间隔**: 设置多少秒检查一次新粉丝（最少30秒）
- **欢迎文字**: 自定义发送给新粉丝的文字消息
- **欢迎图片**: 可选择一张图片发送给新粉丝

### 4. 开始监控

1. 填写所有必需参数
2. 设置欢迎消息内容
3. 点击"🚀 开始监控"按钮
4. 工具会先初始化当前粉丝列表
5. 然后开始定期检查新增粉丝

## 📋 工作流程

```
1. 初始化 → 获取当前所有粉丝列表
2. 定期检查 → 每隔N秒检查一次粉丝列表
3. 发现新粉丝 → 对比找出新增的粉丝
4. 发送欢迎消息 → 自动发送文字和图片消息
5. 记录日志 → 显示详细的操作日志
```

## ⚙️ 高级设置

### 自定义检查间隔
- 最小间隔：30秒
- 推荐间隔：60-300秒
- 间隔太短可能被限制

### 欢迎消息设置
- **文字消息**：支持emoji和换行
- **图片消息**：支持jpg、png等常见格式
- **发送顺序**：先发文字，再发图片

## 📊 监控状态

### 状态显示：
- **未启动**: 监控未开始
- **监控中**: 正在监控新粉丝
- **已停止**: 监控已停止

### 日志信息：
- 🔍 开始监控
- 👤 发现新粉丝
- 📝 发送文字消息
- 🖼️ 发送图片消息
- ✅ 操作成功
- ❌ 操作失败

## 🛠️ 故障排除

### 常见问题：

1. **启动失败**
   - 检查所有必需参数是否填写
   - 确认参数格式正确
   - 检查网络连接

2. **获取粉丝列表失败**
   - 检查GSID、SUB等参数是否过期
   - 确认账号权限正常
   - 可能需要重新抓包获取参数

3. **发送消息失败**
   - 检查消息发送相关参数
   - 确认目标用户可以接收私信
   - 避免发送频率过高

4. **监控中断**
   - 查看日志中的错误信息
   - 检查网络连接稳定性
   - 重新启动监控

### 参数获取方法：

参考主目录下的 `README.md` 文件中关于抓包获取参数的详细说明。

## 📝 注意事项

1. **合规使用**：
   - 遵守微博平台规则
   - 不要发送垃圾信息
   - 控制发送频率

2. **参数安全**：
   - 妥善保管登录凭证
   - 定期更新过期参数
   - 不要分享给他人

3. **监控频率**：
   - 建议间隔60秒以上
   - 避免过于频繁的请求
   - 注意API调用限制

4. **消息内容**：
   - 欢迎消息要友好得体
   - 避免包含敏感内容
   - 图片大小要适中

## 🔧 命令行版本

如果你更喜欢命令行版本，可以直接使用：

```bash
python weibo_follower_monitor.py
```

需要在代码中修改配置参数。

## 📞 技术支持

如果遇到问题：

1. 查看监控日志中的详细错误信息
2. 确认所有参数配置正确
3. 检查网络连接和账号状态
4. 参考故障排除部分的解决方案

## 🎉 使用建议

1. **首次使用**：
   - 先用小间隔测试功能
   - 确认消息发送正常后再正式使用
   - 准备好合适的欢迎图片

2. **长期使用**：
   - 定期检查参数是否过期
   - 根据粉丝增长情况调整间隔
   - 适时更新欢迎消息内容

3. **最佳实践**：
   - 设置友好的欢迎消息
   - 选择有代表性的欢迎图片
   - 保持适度的监控频率
