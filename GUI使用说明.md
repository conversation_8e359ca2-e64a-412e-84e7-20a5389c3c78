# 微博国际版私信发送工具 - GUI版本使用说明

## 🚀 启动方法

```bash
python weibo_sender_gui.py
```

## 📋 界面说明

### 1. 配置参数区域
在使用前，你需要填写以下必需参数：

- **你的UID**: 你的微博用户ID
- **GSID**: 从抓包数据中获取的会话标识符
- **S值**: 签名参数（通常是 `dddddddd`）
- **AID**: 设备标识符，从抓包数据中获取
- **SUB Cookie**: 登录凭证（必需）
- **SUBP Cookie**: 登录凭证（可选，可以留空）

填写完成后点击 **"保存配置"** 按钮，配置会自动保存到 `weibo_config.txt` 文件中，下次启动时会自动加载。

### 2. 发送消息区域

#### 📝 发送文字消息
1. 在 **"接收者UID"** 输入框中填入目标用户的UID
2. 在 **"文字消息"** 文本框中输入要发送的内容
3. 点击 **"📝 发送文字消息"** 按钮

#### 🖼️ 发送图片消息
1. 在 **"接收者UID"** 输入框中填入目标用户的UID
2. 点击 **"📁 选择图片"** 按钮选择要发送的图片文件
3. 支持的图片格式：JPG、JPEG、PNG、GIF、BMP、WEBP
4. 选择完成后会显示文件名
5. 点击 **"📤 发送图片"** 按钮发送
6. 如需重新选择，可点击 **"🗑️ 清除选择"** 按钮

### 3. 操作日志区域
- 显示所有操作的详细日志信息
- 包括成功/失败状态、错误信息等
- 点击 **"清空日志"** 可清除所有日志记录

## 🔧 功能特性

- ✅ **自动保存配置**: 配置参数会自动保存，无需重复输入
- ✅ **多线程发送**: 发送操作在后台线程执行，不会阻塞界面
- ✅ **详细日志**: 实时显示操作状态和结果
- ✅ **错误处理**: 完善的错误提示和异常处理
- ✅ **文件选择**: 直观的图片文件选择界面
- ✅ **参数验证**: 自动检查必需参数是否填写完整

## 📝 使用步骤

### 首次使用
1. 启动程序：`python weibo_sender_gui.py`
2. 在配置参数区域填入你的实际参数
3. 点击"保存配置"按钮
4. 填入接收者UID
5. 发送文字或图片消息

### 后续使用
1. 启动程序（配置会自动加载）
2. 填入接收者UID
3. 直接发送消息

## ⚠️ 注意事项

1. **参数获取**: 所有参数都需要从微博国际版的抓包数据中获取
2. **参数时效**: GSID、SUB等参数有时效性，过期后需要重新获取
3. **发送频率**: 建议控制发送频率，避免被平台限制
4. **网络连接**: 确保网络连接正常
5. **文件大小**: 建议图片文件不要过大

## 🐛 常见问题

### Q: 点击"选择图片"按钮没有反应？
A: 检查日志区域是否有错误信息，可能是文件对话框权限问题。

### Q: 发送失败怎么办？
A: 
1. 检查所有配置参数是否正确
2. 确认参数是否过期，需要重新抓包获取
3. 查看日志区域的详细错误信息

### Q: 配置保存在哪里？
A: 配置保存在程序目录下的 `weibo_config.txt` 文件中。

### Q: 如何获取抓包参数？
A: 参考主目录下的 `README.md` 文件中的详细说明。

## 📞 技术支持

如果遇到问题，请：
1. 查看操作日志中的详细错误信息
2. 确认所有参数填写正确
3. 检查网络连接状态
4. 尝试重新获取抓包参数
