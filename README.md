# 微博国际版私信发送工具

基于抓包分析的微博国际版私信发送工具，支持发送文字和图片消息。

## 功能特性

- ✅ 发送文字消息
- ✅ 发送图片消息
- ✅ 自动处理文件上传流程
- ✅ 支持自定义设备信息

## 必需参数说明

根据抓包分析，你需要提供以下参数：

### 1. 基础参数
- **UID**: 你的微博用户ID
- **GSID**: 会话标识符，从抓包中获取
- **S值**: 签名参数，通常为 `dddddddd`
- **AID**: 设备标识符，格式类似 `01A9Z5FpdQ0Mc4DAWcVITx3M-lP-HshuTAN8wgf9zPiJObjXE.`

### 2. 登录凭证
- **SUB**: 登录Cookie中的SUB值（必需）
- **SUBP**: 登录Cookie中的SUBP值（可选，根据测试可能不需要）

### 3. 设备信息（可选）
- **User-Agent**: 默认使用iOS版本的UA

## 参数获取方法

### 从抓包数据中提取参数：

1. **GSID**: 在请求URL参数中找到 `gsid=` 后面的值
2. **S值**: 在请求URL参数中找到 `s=` 后面的值  
3. **AID**: 在请求URL参数中找到 `aid=` 后面的值
4. **SUB/SUBP**: 在请求头的Cookie中找到

### 示例抓包参数：
```
gsid=_2A25FeyD5DeRxGeBN71IQ8yzPzjqIHXVkETMxrDV6PUJbkdAYLXDCkWpNRGZ6miiZPSS1zqSm2wMq6e1M5iNxerRI
s=dddddddd
aid=01A9Z5FpdQ0Mc4DAWcVITx3M-lP-HshuTAN8wgf9zPiJObjXE.
SUB=_2A25FeyD6DeRhGeBN71IQ8yzPzjqIHXVm12qyrDV8PUJbitAYLVP1kWtNRGZ6mjDBb_wcC9kBcHj9aAaucMH9vBKW
SUBP=0033WrSXqPxfM725Ws9jqgMF55529P9D9WhU8UKkm0nPnqKa9hzwv_oN5NHD95Qce0B7eKeEe0-cWs4DqcjeKJvEdg8V1K-7e0z7
```

## 使用方法

### 1. 安装依赖
```bash
pip install requests
```

### 2. 配置参数
```python
from weibo_sender import WeiboSender

# 配置你的参数
config = {
    'uid': '你的UID',
    'gsid': '你的GSID',
    's_value': 'dddddddd',  # 通常是这个值
    'aid': '你的AID设备标识',
    'sub': '你的SUB_Cookie',
    'subp': None  # 可选参数，可以设为None或实际值
}

# 创建发送器
sender = WeiboSender(
    uid=config['uid'],
    gsid=config['gsid'],
    s_value=config['s_value'],
    aid=config['aid']
)

# 设置登录凭证（SUBP是可选的）
sender.set_cookies(config['sub'], config.get('subp'))
```

### 3. 发送文字消息
```python
recipient_uid = '接收者的UID'
text = '你好，这是一条测试消息！'

result = sender.send_text(recipient_uid, text)
print(result)
```

### 4. 发送图片消息
```python
recipient_uid = '接收者的UID'
image_path = 'path/to/your/image.jpg'

result = sender.send_image(recipient_uid, image_path)
print(result)
```

## API接口分析

### 文字消息接口
- **URL**: `https://api.weibo.com/webim/2/direct_messages/new.json`
- **方法**: POST
- **参数**:
  - `decodetime=1`
  - `is_encoded=0`
  - `media_type=0`
  - `source=209678993`
  - `text=消息内容`
  - `uid=接收者UID`

### 图片消息接口
图片发送需要三步：

#### 1. 初始化上传
- **URL**: `https://upload.api.weibo.com/fileplatform/init.json`
- **方法**: GET
- **主要参数**: 文件MD5、大小、接收者ID等

#### 2. 上传文件数据
- **URL**: `https://upload.api.weibo.com/fileplatform/upload.json`
- **方法**: POST
- **数据**: 二进制文件数据

#### 3. 发送图片消息
- **URL**: `https://api.weibo.com/webim/2/direct_messages/new.json`
- **方法**: POST
- **主要参数**:
  - `media_type=1` (图片消息)
  - `attachment=文件令牌`
  - `uid=接收者UID`

## 注意事项

1. **参数时效性**: GSID、SUB、SUBP等参数有时效性，过期后需要重新获取
2. **请求频率**: 建议控制发送频率，避免被限制
3. **文件格式**: 图片支持常见格式（jpg、png等）
4. **文件大小**: 建议图片文件不要太大，避免上传失败
5. **错误处理**: 代码已包含基本的错误处理，实际使用时可根据需要扩展

## 错误排查

1. **登录失败**: 检查SUB参数是否正确且未过期（SUBP可选）
2. **上传失败**: 检查AID、GSID、S值是否正确
3. **参数错误**: 对比抓包数据，确保所有参数格式正确

## 免责声明

本工具仅供学习和研究使用，请遵守相关法律法规和平台规则。使用本工具产生的任何后果由使用者自行承担。
