#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
微博国际版粉丝监控工具 - GUI版本
监控账号新增粉丝，自动发送欢迎消息
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import threading
import os
import requests
import json
import urllib.parse
from weibo_follower_monitor import WeiboFollowerMonitor


class WeiboMonitorGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("微博国际版粉丝监控工具")
        self.root.geometry("900x800")
        self.root.resizable(True, True)
        
        # 设置样式
        style = ttk.Style()
        style.theme_use('clam')
        
        self.monitor = None
        self.welcome_image_path = None
        
        self.create_widgets()
        self.load_config()
    
    def create_widgets(self):
        """创建界面组件"""
        # 创建主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        
        # 标题
        title_label = ttk.Label(main_frame, text="微博国际版粉丝监控工具", 
                               font=('Arial', 16, 'bold'))
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 20))
        
        # 账号导入区域
        import_frame = ttk.LabelFrame(main_frame, text="账号导入", padding="10")
        import_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        import_frame.columnconfigure(1, weight=1)

        # 账号输入
        ttk.Label(import_frame, text="账号格式:").grid(row=0, column=0, sticky=tk.W, pady=2)
        self.account_var = tk.StringVar(value="**********----_2A25FUEv5DeRxGe5O61QS-SnIyTuIHXVkRNgCrDV6PUJbkdANLW_xkWpNdb9f5qCQjSx0wHS_YSeHTcOahdx04gJE")
        account_entry = ttk.Entry(import_frame, textvariable=self.account_var, width=80)
        account_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(10, 0), pady=2)

        # 格式说明
        ttk.Label(import_frame, text="格式说明:", foreground="gray").grid(row=1, column=0, sticky=tk.W, pady=2)
        ttk.Label(import_frame, text="UID----GSID", foreground="gray").grid(row=1, column=1, sticky=tk.W, padx=(10, 0), pady=2)

        # 导入按钮
        button_frame = ttk.Frame(import_frame)
        button_frame.grid(row=2, column=1, sticky=tk.E, pady=(10, 0))

        ttk.Button(button_frame, text="🔍 解析账号", command=self.parse_account).grid(row=0, column=0, padx=(0, 10))
        ttk.Button(button_frame, text="📋 从剪贴板导入", command=self.import_from_clipboard).grid(row=0, column=1)

        # 解析结果区域
        result_frame = ttk.LabelFrame(main_frame, text="解析结果", padding="10")
        result_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        result_frame.columnconfigure(1, weight=1)

        # UID
        ttk.Label(result_frame, text="UID:").grid(row=0, column=0, sticky=tk.W, pady=2)
        self.uid_var = tk.StringVar()
        ttk.Entry(result_frame, textvariable=self.uid_var, width=50, state='readonly').grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(10, 0), pady=2)

        # GSID (SUB)
        ttk.Label(result_frame, text="GSID/SUB:").grid(row=1, column=0, sticky=tk.W, pady=2)
        self.gsid_var = tk.StringVar()
        ttk.Entry(result_frame, textvariable=self.gsid_var, width=50, state='readonly').grid(row=1, column=1, sticky=(tk.W, tk.E), padx=(10, 0), pady=2)

        # AID
        ttk.Label(result_frame, text="AID:").grid(row=2, column=0, sticky=tk.W, pady=2)
        self.aid_var = tk.StringVar()
        aid_frame = ttk.Frame(result_frame)
        aid_frame.grid(row=2, column=1, sticky=(tk.W, tk.E), padx=(10, 0), pady=2)
        aid_frame.columnconfigure(0, weight=1)

        ttk.Entry(aid_frame, textvariable=self.aid_var, width=50, state='readonly').grid(row=0, column=0, sticky=(tk.W, tk.E))
        self.get_aid_button = ttk.Button(aid_frame, text="获取AID", command=self.get_aid, state='disabled')
        self.get_aid_button.grid(row=0, column=1, padx=(10, 0))

        # S值
        ttk.Label(result_frame, text="S值:").grid(row=3, column=0, sticky=tk.W, pady=2)
        self.s_value_var = tk.StringVar()
        s_frame = ttk.Frame(result_frame)
        s_frame.grid(row=3, column=1, sticky=(tk.W, tk.E), padx=(10, 0), pady=2)
        s_frame.columnconfigure(0, weight=1)

        ttk.Entry(s_frame, textvariable=self.s_value_var, width=50, state='readonly').grid(row=0, column=0, sticky=(tk.W, tk.E))
        self.calc_s_button = ttk.Button(s_frame, text="计算S值", command=self.calculate_s_value, state='disabled')
        self.calc_s_button.grid(row=0, column=1, padx=(10, 0))

        # 状态显示
        self.parse_status_var = tk.StringVar(value="请输入账号信息并点击解析")
        ttk.Label(result_frame, textvariable=self.parse_status_var, foreground="blue").grid(row=4, column=1, sticky=tk.W, padx=(10, 0), pady=(10, 0))
        
        # 监控设置区域
        monitor_frame = ttk.LabelFrame(main_frame, text="监控设置", padding="10")
        monitor_frame.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        monitor_frame.columnconfigure(1, weight=1)
        
        # 检查间隔
        ttk.Label(monitor_frame, text="检查间隔(秒):").grid(row=0, column=0, sticky=tk.W, pady=2)
        self.interval_var = tk.StringVar(value="60")
        ttk.Entry(monitor_frame, textvariable=self.interval_var, width=10).grid(row=0, column=1, sticky=tk.W, padx=(10, 0), pady=2)
        
        # 欢迎消息设置区域
        welcome_frame = ttk.LabelFrame(main_frame, text="欢迎消息设置", padding="10")
        welcome_frame.grid(row=4, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        welcome_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(4, weight=1)
        
        # 欢迎文字
        ttk.Label(welcome_frame, text="欢迎文字:").grid(row=0, column=0, sticky=(tk.W, tk.N), pady=2)
        self.welcome_text = scrolledtext.ScrolledText(welcome_frame, height=4, width=50)
        self.welcome_text.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(10, 0), pady=2)
        self.welcome_text.insert(tk.END, "感谢关注！欢迎来到我的微博！🎉")
        
        # 欢迎图片
        ttk.Label(welcome_frame, text="欢迎图片:").grid(row=1, column=0, sticky=tk.W, pady=(10, 2))
        image_frame = ttk.Frame(welcome_frame)
        image_frame.grid(row=1, column=1, sticky=(tk.W, tk.E), padx=(10, 0), pady=(10, 2))
        image_frame.columnconfigure(0, weight=1)
        
        self.image_path_var = tk.StringVar(value="未选择图片")
        ttk.Label(image_frame, textvariable=self.image_path_var, foreground="gray").grid(row=0, column=0, sticky=(tk.W, tk.E))
        ttk.Button(image_frame, text="选择图片", command=self.select_welcome_image).grid(row=0, column=1, padx=(10, 0))
        ttk.Button(image_frame, text="清除", command=self.clear_welcome_image).grid(row=0, column=2, padx=(5, 0))
        
        # 控制按钮区域
        control_frame = ttk.Frame(main_frame)
        control_frame.grid(row=5, column=0, columnspan=2, pady=(0, 10))
        
        self.start_button = ttk.Button(control_frame, text="🚀 开始监控", command=self.start_monitoring)
        self.start_button.grid(row=0, column=0, padx=(0, 10))
        
        self.stop_button = ttk.Button(control_frame, text="🛑 停止监控", command=self.stop_monitoring, state='disabled')
        self.stop_button.grid(row=0, column=1, padx=(0, 10))
        
        ttk.Button(control_frame, text="💾 保存配置", command=self.save_config).grid(row=0, column=2, padx=(0, 10))
        
        # 状态显示
        self.status_var = tk.StringVar(value="状态: 未启动")
        ttk.Label(control_frame, textvariable=self.status_var, font=('Arial', 10, 'bold')).grid(row=0, column=3, padx=(20, 0))
        
        # 日志区域
        log_frame = ttk.LabelFrame(main_frame, text="监控日志", padding="10")
        log_frame.grid(row=6, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S))
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
        main_frame.rowconfigure(6, weight=1)
        
        self.log_text = scrolledtext.ScrolledText(log_frame, height=12, width=80)
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 清空日志按钮
        ttk.Button(log_frame, text="清空日志", command=self.clear_log).grid(row=1, column=0, sticky=tk.E, pady=(5, 0))
    
    def log_message(self, message):
        """添加日志消息"""
        self.log_text.insert(tk.END, f"{message}\n")
        self.log_text.see(tk.END)
        self.root.update_idletasks()
    
    def clear_log(self):
        """清空日志"""
        self.log_text.delete(1.0, tk.END)
    
    def select_welcome_image(self):
        """选择欢迎图片"""
        file_path = filedialog.askopenfilename(
            title="选择欢迎图片",
            filetypes=[
                ("图片文件", "*.jpg *.jpeg *.png *.gif *.bmp *.webp"),
                ("所有文件", "*.*")
            ]
        )
        
        if file_path:
            self.welcome_image_path = file_path
            filename = os.path.basename(file_path)
            self.image_path_var.set(f"已选择: {filename}")
            self.log_message(f"📁 已选择欢迎图片: {filename}")
    
    def clear_welcome_image(self):
        """清除欢迎图片"""
        self.welcome_image_path = None
        self.image_path_var.set("未选择图片")
        self.log_message("🗑️ 已清除欢迎图片")

    def import_from_clipboard(self):
        """从剪贴板导入账号"""
        try:
            clipboard_text = self.root.clipboard_get()
            if clipboard_text:
                self.account_var.set(clipboard_text.strip())
                self.log_message("📋 已从剪贴板导入账号信息")
            else:
                messagebox.showwarning("警告", "剪贴板为空")
        except Exception as e:
            messagebox.showerror("错误", f"读取剪贴板失败: {e}")

    def parse_account(self):
        """解析账号信息"""
        account = self.account_var.get().strip()

        if not account:
            messagebox.showerror("错误", "请输入账号信息")
            return

        if '----' not in account:
            messagebox.showerror("错误", "账号格式错误，应为 UID----GSID")
            return

        try:
            uid, gsid = account.split('----', 1)

            if not uid or not gsid:
                messagebox.showerror("错误", "UID或GSID不能为空")
                return

            # 更新界面
            self.uid_var.set(uid)
            self.gsid_var.set(gsid)
            self.aid_var.set("")
            self.s_value_var.set("")

            # 启用按钮
            self.get_aid_button.config(state='normal')
            self.calc_s_button.config(state='disabled')  # 需要先获取AID

            self.parse_status_var.set("✅ 账号解析成功，请获取AID")
            self.log_message(f"✅ 账号解析成功: UID={uid}")

        except Exception as e:
            messagebox.showerror("错误", f"解析账号失败: {e}")

    def get_aid(self):
        """获取AID"""
        self.parse_status_var.set("🔍 正在获取AID...")
        self.get_aid_button.config(state='disabled')

        # 在新线程中获取AID
        threading.Thread(target=self._get_aid_thread, daemon=True).start()

    def _get_aid_thread(self):
        """在线程中获取AID"""
        try:
            # 第一步：获取登录URL
            url1 = "http://43.241.51.137:5454/asd/dfgh/?uid=aid"

            self.log_message(f"🔍 正在请求: {url1}")
            response1 = requests.get(url1, timeout=10)
            if response1.status_code != 200:
                raise Exception(f"获取登录URL失败: HTTP {response1.status_code}")

            try:
                data1 = response1.json()
            except json.JSONDecodeError:
                raise Exception(f"第一步响应不是有效JSON: {response1.text[:200]}")

            self.log_message(f"📋 第一步响应: {data1}")

            if data1.get('code') != 200:
                raise Exception(f"API返回错误: {data1.get('msg', '未知错误')}")

            if 'result' not in data1 or 'url' not in data1['result']:
                raise Exception(f"响应格式错误，缺少result.url: {list(data1.keys())}")

            login_url = data1['result']['url']
            self.log_message(f"🔗 获得登录URL: {login_url[:100]}...")

            # 第二步：访问登录URL获取AID
            response2 = requests.get(login_url, timeout=10)
            if response2.status_code != 200:
                raise Exception(f"访问登录URL失败: HTTP {response2.status_code}")

            try:
                data2 = response2.json()
            except json.JSONDecodeError:
                raise Exception(f"第二步响应不是有效JSON: {response2.text[:200]}")

            self.log_message(f"📋 第二步响应: {list(data2.keys()) if isinstance(data2, dict) else type(data2)}")

            if 'aid' not in data2:
                raise Exception(f"响应中未找到AID，可用字段: {list(data2.keys()) if isinstance(data2, dict) else '非字典类型'}")

            aid = data2['aid']
            self.log_message(f"✅ 成功获取AID: {aid}")

            # 更新界面
            self.root.after(0, lambda: self._update_aid_result(aid, True))

        except Exception as e:
            error_msg = str(e)
            self.log_message(f"❌ AID获取过程出错: {error_msg}")
            self.root.after(0, lambda: self._update_aid_result(error_msg, False))

    def _update_aid_result(self, result, success):
        """更新AID获取结果"""
        if success:
            self.aid_var.set(result)
            self.parse_status_var.set("✅ AID获取成功，请计算S值")
            self.calc_s_button.config(state='normal')
            self.log_message(f"✅ AID获取成功: {result}")
        else:
            self.parse_status_var.set(f"❌ AID获取失败: {result}")
            self.get_aid_button.config(state='normal')
            self.log_message(f"❌ AID获取失败: {result}")

    def calculate_s_value(self):
        """计算S值"""
        uid = self.uid_var.get()
        gsid = self.gsid_var.get()
        aid = self.aid_var.get()

        if not all([uid, gsid, aid]):
            messagebox.showerror("错误", "请先完成账号解析和AID获取")
            return

        self.parse_status_var.set("🔍 正在计算S值...")
        self.calc_s_button.config(state='disabled')

        # 在新线程中计算S值
        threading.Thread(target=self._calculate_s_thread, daemon=True).start()

    def _calculate_s_thread(self):
        """在线程中计算S值"""
        uid = self.uid_var.get()
        gsid = self.gsid_var.get()
        aid = self.aid_var.get()

        try:
            url = "https://api.weibo.cn/2/users/show"
            headers = {
                "User-Agent": "WeiboOverseas/6.7.9 (iPhone; iOS 18.6.1; Scale/2.00)",
                "Referer": "https://api.weibo.cn/2/users/show"
            }

            # 尝试不同的S值
            s_values = [c*8 for c in "abcdef0123456789"]

            found_s = None
            for s in s_values:
                params = {
                    "uid": uid,
                    "gsid": gsid,
                    "s": s,
                    "c": "weicoabroad",
                    "from": "12DC193010",
                    "ua": "iPhone13,4_iOS18.5_Weibo_intl._6790_wifi__iphone__os18.5",
                    "v_p": "59",
                    "lang": "zh_CN",
                    "aid": aid
                }

                try:
                    resp = requests.get(url, headers=headers, params=params, timeout=8)
                    text = resp.text

                    # 只要不是客户端身份校验失败，就判定为S值正确
                    if resp.status_code == 200 and ('客户端身份校验失败' not in text and '"errno":-105' not in text):
                        found_s = s
                        break

                except Exception:
                    continue

            # 更新界面
            if found_s:
                self.root.after(0, lambda: self._update_s_result(found_s, True))
            else:
                self.root.after(0, lambda: self._update_s_result("未找到有效的S值", False))

        except Exception as e:
            error_msg = str(e)
            self.root.after(0, lambda: self._update_s_result(error_msg, False))

    def _update_s_result(self, result, success):
        """更新S值计算结果"""
        if success:
            self.s_value_var.set(result)
            self.parse_status_var.set("✅ 所有参数获取完成，可以开始监控")
            self.log_message(f"✅ S值计算成功: {result}")
        else:
            self.parse_status_var.set(f"❌ S值计算失败: {result}")
            self.calc_s_button.config(state='normal')
            self.log_message(f"❌ S值计算失败: {result}")
    
    def validate_config(self):
        """验证配置"""
        if not all([self.uid_var.get(), self.gsid_var.get(), self.s_value_var.get(), self.aid_var.get()]):
            messagebox.showerror("错误", "请完成账号解析并获取所有必需参数！")
            return False

        try:
            interval = int(self.interval_var.get())
            if interval < 30:
                messagebox.showerror("错误", "检查间隔不能少于30秒！")
                return False
        except ValueError:
            messagebox.showerror("错误", "检查间隔必须是数字！")
            return False

        return True
    
    def start_monitoring(self):
        """开始监控"""
        if not self.validate_config():
            return
        
        try:
            # 创建监控器
            self.monitor = WeiboFollowerMonitor(
                uid=self.uid_var.get(),
                gsid=self.gsid_var.get(),
                s_value=self.s_value_var.get(),
                aid=self.aid_var.get(),
                sub=self.gsid_var.get(),  # SUB等于GSID
                subp=None  # 不使用SUBP
            )
            
            # 设置日志回调
            self.monitor.set_log_callback(self.log_message)
            
            # 设置欢迎消息
            welcome_text = self.welcome_text.get(1.0, tk.END).strip()
            self.monitor.set_welcome_message(welcome_text, self.welcome_image_path)
            
            # 设置检查间隔
            interval = int(self.interval_var.get())
            self.monitor.set_check_interval(interval)
            
            # 在新线程中启动监控
            def start_monitor_thread():
                if self.monitor.start_monitoring():
                    self.root.after(0, lambda: self.update_ui_state(True))
                else:
                    self.root.after(0, lambda: messagebox.showerror("错误", "启动监控失败！"))
            
            threading.Thread(target=start_monitor_thread, daemon=True).start()
            
        except Exception as e:
            messagebox.showerror("错误", f"启动监控时出错: {e}")
    
    def stop_monitoring(self):
        """停止监控"""
        if self.monitor:
            self.monitor.stop_monitoring()
            self.monitor = None
        
        self.update_ui_state(False)
    
    def update_ui_state(self, is_monitoring):
        """更新UI状态"""
        if is_monitoring:
            self.start_button.config(state='disabled')
            self.stop_button.config(state='normal')
            self.status_var.set("状态: 监控中...")
        else:
            self.start_button.config(state='normal')
            self.stop_button.config(state='disabled')
            self.status_var.set("状态: 已停止")
    
    def save_config(self):
        """保存配置"""
        config_data = {
            'account': self.account_var.get(),
            'uid': self.uid_var.get(),
            'gsid': self.gsid_var.get(),
            's_value': self.s_value_var.get(),
            'aid': self.aid_var.get(),
            'interval': self.interval_var.get(),
            'welcome_text': self.welcome_text.get(1.0, tk.END).strip(),
            'welcome_image': self.welcome_image_path or ''
        }
        
        try:
            with open('monitor_config.txt', 'w', encoding='utf-8') as f:
                for key, value in config_data.items():
                    f.write(f"{key}={value}\n")
            self.log_message("✅ 配置已保存到 monitor_config.txt")
            messagebox.showinfo("成功", "配置已保存！")
        except Exception as e:
            self.log_message(f"❌ 保存配置失败: {e}")
            messagebox.showerror("错误", f"保存配置失败: {e}")
    
    def load_config(self):
        """加载配置"""
        try:
            if os.path.exists('monitor_config.txt'):
                with open('monitor_config.txt', 'r', encoding='utf-8') as f:
                    for line in f:
                        line = line.strip()
                        if '=' in line:
                            key, value = line.split('=', 1)
                            if key == 'account':
                                self.account_var.set(value)
                            elif key == 'uid':
                                self.uid_var.set(value)
                            elif key == 'gsid':
                                self.gsid_var.set(value)
                            elif key == 's_value':
                                self.s_value_var.set(value)
                            elif key == 'aid':
                                self.aid_var.set(value)
                            elif key == 'interval':
                                self.interval_var.set(value)
                            elif key == 'welcome_text':
                                self.welcome_text.delete(1.0, tk.END)
                                self.welcome_text.insert(1.0, value)
                            elif key == 'welcome_image' and value:
                                self.welcome_image_path = value
                                filename = os.path.basename(value)
                                self.image_path_var.set(f"已选择: {filename}")

                # 如果有完整配置，启用按钮
                if all([self.uid_var.get(), self.gsid_var.get(), self.aid_var.get(), self.s_value_var.get()]):
                    self.get_aid_button.config(state='normal')
                    self.calc_s_button.config(state='normal')
                    self.parse_status_var.set("✅ 配置已加载完成")

                self.log_message("✅ 已加载保存的配置")
        except Exception as e:
            self.log_message(f"⚠️ 加载配置失败: {e}")


def main():
    """主函数"""
    root = tk.Tk()
    app = WeiboMonitorGUI(root)
    
    # 设置窗口关闭事件
    def on_closing():
        if app.monitor:
            app.stop_monitoring()
        root.destroy()
    
    root.protocol("WM_DELETE_WINDOW", on_closing)
    
    # 启动GUI
    root.mainloop()


if __name__ == '__main__':
    main()
