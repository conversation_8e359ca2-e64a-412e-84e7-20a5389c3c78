#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置示例文件
请复制此文件为 config.py 并填入你的实际参数
"""

# 微博账号配置
WEIBO_CONFIG = {
    # 你的微博UID
    'uid': '你的UID',
    
    # 从抓包中获取的GSID（会话标识）
    'gsid': '_2A25FeyD5DeRxGeBN71IQ8yzPzjqIHXVkETMxrDV6PUJbkdAYLXDCkWpNRGZ6miiZPSS1zqSm2wMq6e1M5iNxerRI',
    
    # S值（通常是dddddddd）
    's_value': 'dddddddd',
    
    # AID设备标识符
    'aid': '01A9Z5FpdQ0Mc4DAWcVITx3M-lP-HshuTAN8wgf9zPiJObjXE.',
    
    # 登录Cookie - SUB值（必需）
    'sub': '_2A25FeyD6DeRhGeBN71IQ8yzPzjqIHXVm12qyrDV8PUJbitAYLVP1kWtNRGZ6mjDBb_wcC9kBcHj9aAaucMH9vBKW',

    # 登录Cookie - SUBP值（可选，根据测试可能不需要）
    'subp': None,  # 可以设为None或填入实际值：'0033WrSXqPxfM725Ws9jqgMF55529P9D9WhU8UKkm0nPnqKa9hzwv_oN5NHD95Qce0B7eKeEe0-cWs4DqcjeKJvEdg8V1K-7e0z7',
    
    # User-Agent（可选，有默认值）
    'user_agent': 'WeiboOverseas/6.7.9 (com.weibo.international; build:*******; iOS 18.5.0) Alamofire/5.10.2'
}

# 接收者配置
RECIPIENTS = {
    # 可以配置多个接收者
    'test_user': '7993067096',  # 示例接收者UID
    # 'friend1': '其他用户UID',
    # 'friend2': '其他用户UID',
}

# 使用示例
if __name__ == '__main__':
    from weibo_sender import WeiboSender
    import os
    
    # 创建发送器
    sender = WeiboSender(
        uid=WEIBO_CONFIG['uid'],
        gsid=WEIBO_CONFIG['gsid'],
        s_value=WEIBO_CONFIG['s_value'],
        aid=WEIBO_CONFIG['aid'],
        user_agent=WEIBO_CONFIG.get('user_agent')
    )
    
    # 设置登录凭证（SUBP是可选的）
    sender.set_cookies(WEIBO_CONFIG['sub'], WEIBO_CONFIG.get('subp'))
    
    # 发送测试消息
    recipient_uid = RECIPIENTS['test_user']
    
    # 发送文字
    print("发送文字消息...")
    text_result = sender.send_text(recipient_uid, '这是一条测试消息 🎉')
    print(f"文字发送结果: {text_result}")
    
    # 发送图片（如果存在测试图片）
    test_image = 'test.jpg'
    if os.path.exists(test_image):
        print("发送图片消息...")
        image_result = sender.send_image(recipient_uid, test_image)
        print(f"图片发送结果: {image_result}")
    else:
        print(f"测试图片不存在: {test_image}")
        print("请将要发送的图片重命名为 test.jpg 放在当前目录下进行测试")
